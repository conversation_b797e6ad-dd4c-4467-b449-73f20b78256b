/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.querysubbalance;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceResponse.SubBalanceDtlBean;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.business.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.repository.SubCustBooksRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:(查询子账本明细)
 * @reason:
 * @date 2018年4月12日 下午3:40:47
 * @since JDK 1.6
 */
@DubboService
@Service("querySubBalanceFacade")
public class QuerySubBalanceFacadeService extends AbstractBusiProcess implements QuerySubBalanceFacade {

    private static final Logger logger = LogManager.getLogger(QuerySubBalanceFacadeService.class);

    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QuerySubBalanceFacadeService
     * @apiName execute
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} fundShareClass 产品份额类型，A-前收费；B-后收费
     * @apiParam (请求参数) {String} protocolNo 协议号
     * @apiParam (请求参数) {String} protocolType 4-高端协议类型
     * @apiParam (请求参数) {String} cpAcctNo 资金账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * fundShareClass=U85VbRU9O&hbOneNo=R&pageSize=5565&protocolType=V&disCode=nfISA&txChannel=moiH9&appTm=7yW&productCode=LMEl4&disCodeList=FzBzd&subOutletCode=tX8dG&pageNo=2469&operIp=IlguM&protocolNo=cLIOqSv&txAcctNo=K&cpAcctNo=Pk6Az&appDt=7Zc&dataTrack=Lk&txCode=TMV5EH&outletCode=9gnpm
     * @apiSuccess (响应结果) {Array} balanceDtlList
     * @apiSuccess (响应结果) {String} balanceDtlList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} balanceDtlList.disCode 分销机构号
     * @apiSuccess (响应结果) {String} balanceDtlList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceDtlList.fundShareClass 份额类型
     * @apiSuccess (响应结果) {String} balanceDtlList.productName 产品名称
     * @apiSuccess (响应结果) {String} balanceDtlList.productType 产品类型
     * @apiSuccess (响应结果) {Number} balanceDtlList.nav 净值
     * @apiSuccess (响应结果) {String} balanceDtlList.productStatus 产品状态：0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记； 8-红利发放；          9-基金封闭； a-基金终止
     * @apiSuccess (响应结果) {String} balanceDtlList.navDt 净值日期
     * @apiSuccess (响应结果) {String} balanceDtlList.buyStatus 购买状态：1-认购，2-申购，3-不可购买
     * @apiSuccess (响应结果) {String} balanceDtlList.redeemStatus 赎回状态 1-可赎回，2-不可赎回
     * @apiSuccess (响应结果) {String} balanceDtlList.protocolNo 协议号
     * @apiSuccess (响应结果) {String} balanceDtlList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} balanceDtlList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} balanceDtlList.bankName 银行名称
     * @apiSuccess (响应结果) {String} balanceDtlList.bankAcctNo 银行卡号
     * @apiSuccess (响应结果) {Number} balanceDtlList.balanceVol 总份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.availVol 可用份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.unconfirmedVol 待确认份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.marketValue 市值
     * @apiSuccess (响应结果) {String} balanceDtlList.openRedeDt 开放赎回日期
     * @apiSuccess (响应结果) {String} balanceDtlList.productChannel 产品通道  3-群济私募 5-好买公募 6-高端公募
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"EakOwcMaV","totalPage":9691,"pageNo":9000,"description":"bSD","balanceDtlList":[{"bankCode":"YjuO73","fundShareClass":"E","nav":8841.************,"bankAcctNo":"z2FE","productChannel":"5D4p","navDt":"XF","productStatus":"Ha","balanceVol":9593.************,"marketValue":4013.************,"bankName":"LP2Gq1ZVw","disCode":"DWOuN","productName":"2","productCode":"uWbn79r","unconfirmedVol":5369.**********,"availVol":8129.************,"openRedeDt":"CLN5LAysT4","txAcctNo":"d","buyStatus":"A6tpu","redeemStatus":"mCdR0lSD7","protocolNo":"DP7","cpAcctNo":"TMrrx","productType":"k4xC"}],"totalCount":5610}
     */
    @Override
    public QuerySubBalanceResponse execute(QuerySubBalanceRequest request) {
        String txAcctNo = request.getTxAcctNo();
        List<String> disCodeList = getDisCodeList(request);
        String productCode = request.getProductCode();
        String cpAcctNo = request.getCpAcctNo();
        String outletCode = request.getOutletCode();
        QuerySubBalanceResponse response = new QuerySubBalanceResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 查询子账本表(开放赎回日期维度)
        List<SubCustBooksPo> booklist = subCustBooksRepository.selectSubCustBooksByOpenRedeDt(disCodeList, txAcctNo, productCode, cpAcctNo, null);
        logger.info("SubCustBooksPo booklist:{}",JSON.toJSONString(booklist));
        //查询专户持仓
        List<SubCustBooksPo> zhBalanceList = subCustBooksRepository.selectZhBalanceDtlWithOutSubBook(disCodeList, txAcctNo, productCode, cpAcctNo);
        logger.info("BalanceVo zhBalanceList{}",JSON.toJSONString(zhBalanceList));
        if(CollectionUtils.isEmpty(booklist)){
            booklist = new ArrayList<SubCustBooksPo>();
        }
        // 排除有子账本的专户持仓
        if (!CollectionUtils.isEmpty(zhBalanceList)) {
            booklist.addAll(zhBalanceList);
        }
        if (CollectionUtils.isEmpty(booklist)) {
            return response;
        }
        // 资金账号set
        Set<String> cpAcctNoSet = new HashSet<>();
        // 产品代码set
        Set<String> fundCodeSet = new HashSet<>();
        List<SubCustBooksPo> list = new ArrayList<SubCustBooksPo>();
        for (SubCustBooksPo book : booklist) {
            list.add(book);
            cpAcctNoSet.add(book.getCpAcctNo());
            fundCodeSet.add(book.getFundCode());
        }
        // 获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<>();
        CountDownLatch latch = new CountDownLatch(cpAcctNoSet.size());
        for (Object value : cpAcctNoSet.toArray()) {
            String cpAcct = (String) value;
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, txAcctNo, disCodeList.get(0), cpAcct, bankCardInfo, outletCode));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QuerySubBalanceFacadeService|latch|", e);
            Thread.currentThread().interrupt();
        }
        // TA交易日
        final String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());
        // 获取产品净值信息
        Map<String, HighProductNavBean> fundNavInfoMap = getHighProductNavMap(new ArrayList<String>(fundCodeSet));
        //获取产品状态信息
        Map<String, HighProductStatInfoBean> highProductStatMap = getHighProductStatInfoMap(new ArrayList<String>(fundCodeSet), taTradeDt);
        //批量查询产品基本信息
        Map<String, HighProductBaseInfoBean> productInfoMap = getHighProductBaseMap(new ArrayList<String>(fundCodeSet));
        List<SubBalanceDtlBean> balanceDtlList = new ArrayList<SubBalanceDtlBean>();
        for (SubCustBooksPo book : list) {
            buildSubCustBookInfo(txAcctNo, disCodeList, bankCardMap, fundNavInfoMap, highProductStatMap, productInfoMap, balanceDtlList, book);
        }
        response.setBalanceDtlList(balanceDtlList);
        return response;

    }

    /**
     * 设置子账本信息
     */
    private void buildSubCustBookInfo(String txAcctNo, List<String> disCodeList, Map<String, QueryCustBankCardResult> bankCardMap, Map<String, HighProductNavBean> fundNavInfoMap, Map<String, HighProductStatInfoBean> highProductStatMap, Map<String, HighProductBaseInfoBean> productInfoMap, List<SubBalanceDtlBean> balanceDtlList, SubCustBooksPo book) {
        SubBalanceDtlBean bean = new SubBalanceDtlBean();
        bean.setTxAcctNo(txAcctNo);
        bean.setDisCode(disCodeList.get(0));
        bean.setProductCode(book.getFundCode());
        bean.setCpAcctNo(book.getCpAcctNo());
        bean.setProtocolNo(book.getProtocolNo());
        bean.setOpenRedeDt(book.getOpenRedeDt());
        // 基金及净值信息
        BigDecimal availVol = book.getBalanceVol().subtract(book.getFrznVol()).subtract(book.getJustFrznVol());
        bean.setAvailVol(MoneyUtil.formatMoney(availVol, 2));
        HighProductNavBean fundInfoBean = fundNavInfoMap.get(book.getFundCode());
        if (fundInfoBean != null) {
            bean.setNav(fundInfoBean.getNav());
            bean.setNavDt(fundInfoBean.getNavDate());
            if (fundInfoBean.getNav() != null) {
                bean.setMarketValue(MoneyUtil.formatMoney(availVol.multiply(fundInfoBean.getNav()), 2));
            }
        }
        HighProductStatInfoBean highProductStatInfoBean = highProductStatMap.get(book.getFundCode());
        if (highProductStatInfoBean != null) {
            bean.setBuyStatus(highProductStatInfoBean.getBuyStatus());
            bean.setProductStatus(highProductStatInfoBean.getFundStat());
            bean.setRedeemStatus(highProductStatInfoBean.getRedeemStatus());
        }
        //基金信息
        HighProductBaseInfoBean highProductBaseInfoBean = productInfoMap.get(book.getFundCode());
        if (highProductBaseInfoBean != null) {
            bean.setProductName(highProductBaseInfoBean.getFundAttr());
            bean.setProductType(highProductBaseInfoBean.getFundType());
            bean.setFundShareClass(highProductBaseInfoBean.getShareClass());
            bean.setProductChannel(highProductBaseInfoBean.getProductChannel());
        }
        bean.setBalanceVol(MoneyUtil.formatMoney(book.getBalanceVol(), 2));
        bean.setUnconfirmedVol(MoneyUtil.formatMoney(book.getFrznVol(), 2));
        // 银行卡信息
        QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
        if (result != null) {
            bean.setBankCode(result.getBankCode());
            bean.setBankName(result.getBankRegionName());
            bean.setBankAcctNo(result.getBankAcct());
        }
        balanceDtlList.add(bean);
    }

    private List<String> getDisCodeList(QuerySubBalanceRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }

    /**
     * getHighProductStatInfoMap:(获取产品状态map)
     *
     * @param midProductCodes
     * @param taTradeDt
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午4:22:19
     */
    private Map<String, HighProductStatInfoBean> getHighProductStatInfoMap(List<String> midProductCodes, String taTradeDt) {

        List<HighProductStatInfoBean> highProductStatInfoBeanList = queryHighProductOuterService.getBatchProductStatInfo(midProductCodes, taTradeDt);
        Map<String, HighProductStatInfoBean> highProductStatInfoMap = new HashMap<String, HighProductStatInfoBean>();
        if (CollectionUtils.isNotEmpty(highProductStatInfoBeanList)) {
            for (HighProductStatInfoBean highProductStatInfoBean : highProductStatInfoBeanList) {
                highProductStatInfoMap.put(highProductStatInfoBean.getFundCode(), highProductStatInfoBean);
            }
        }
        return highProductStatInfoMap;
    }

    /**
     * getHighProductBaseMap:(获取产品基本信息map)
     *
     * @param midProductIds
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午5:27:14
     */
    private Map<String, HighProductBaseInfoBean> getHighProductBaseMap(List<String> midProductIds) {
        Map<String, HighProductBaseInfoBean> highProductBaseMap = new HashMap<String, HighProductBaseInfoBean>();
        List<HighProductBaseInfoBean> highProductBaseList = queryHighProductOuterService.getHighProductBaseInfoList(midProductIds);
        if (CollectionUtils.isNotEmpty(highProductBaseList)) {
            for (HighProductBaseInfoBean highProductBaseInfoBean : highProductBaseList) {
                highProductBaseMap.put(highProductBaseInfoBean.getFundCode(), highProductBaseInfoBean);
            }

        }
        return highProductBaseMap;
    }

}

