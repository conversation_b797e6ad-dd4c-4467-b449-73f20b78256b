<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>acc-common</artifactId>
		<groupId>com.howbuy.acc</groupId>
		<version>3.5.9-RELEASE</version>
	</parent>
	<artifactId>acc-common-utils</artifactId>
	<name>acc-common-utils</name>
	<dependencies>
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>tomcat</groupId>
			<artifactId>servlet-api</artifactId>
			<version>5.5.23</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>webcommon</artifactId>
			<version>V1.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>3.2.12.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.acc</groupId>
			<artifactId>acc-common-cache</artifactId>
		</dependency>
	</dependencies>
</project>