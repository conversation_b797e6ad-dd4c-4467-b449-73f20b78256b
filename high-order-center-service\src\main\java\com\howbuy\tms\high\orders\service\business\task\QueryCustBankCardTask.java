/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;

import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询客户银行卡信息任务
 * 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年4月12日 下午7:46:40
 * @since JDK 1.7
 */
public class QueryCustBankCardTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryCustBankCardTask.class);

    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    private CountDownLatch latch;
    private String txAcctNo;
    private String disCode;
    private String cpAcctNo;
    private String outletCode;
    private QueryCustBankCardResult bankCardInfo;

    public QueryCustBankCardTask(QueryCustBankCardOuterService queryCustBankCardOuterService, CountDownLatch latch, String txAcctNo, String disCode,
            String cpAcctNo, QueryCustBankCardResult bankCardInfo, String outletCode) {
        this.queryCustBankCardOuterService = queryCustBankCardOuterService;
        this.latch = latch;
        this.txAcctNo = txAcctNo;
        this.disCode = disCode;
        this.cpAcctNo = cpAcctNo;
        this.bankCardInfo = bankCardInfo;
        this.outletCode = outletCode;
    }


    @Override
    public RuntimeException call() throws Exception {
        try {
            QueryCustBankCardContext ctx = new QueryCustBankCardContext();
            ctx.setCpAcctNo(cpAcctNo);
            ctx.setDisCode(disCode);
            ctx.setTxAcctNo(txAcctNo);
            ctx.setOutletCode(outletCode);
            QueryCustBankCardResult result = queryCustBankCardOuterService.queryCudtBankCard(ctx);
            BeanUtils.copyProperties(result, bankCardInfo);
        } catch (RuntimeException e) {
            logger.error("QueryCustBankCardTask|RuntimeException.", e);
            return e;
        } finally {
            latch.countDown();
        }
        return null;

    }

}

