/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;

import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询高端产品信息任务类
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月12日 下午5:52:16
 * @since JDK 1.7
 */
public class QueryProductInfoTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryProductInfoTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;

    private HighProductBaseInfoBean highProductBaseBean;

    private CountDownLatch latch;

    public QueryProductInfoTask(QueryHighProductOuterService queryHighProductOuterService, HighProductBaseInfoBean highProductBaseBean, 
            CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.highProductBaseBean = highProductBaseBean;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            HighProductBaseInfoBean bean = queryHighProductOuterService.getHighProductBaseInfo(highProductBaseBean.getFundCode());
            if (bean == null) {
                return null;
            }
            
            BeanUtils.copyProperties(bean, highProductBaseBean);
        }catch(RuntimeException ex){
            logger.error("QueryProductInfoTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

