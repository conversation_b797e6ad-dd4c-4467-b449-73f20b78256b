/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * 
 * @description:(查询开放日历)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月5日 上午10:35:06
 * @since JDK 1.6
 */
public class QueryProductAppointmentInfoTask implements Callable<RuntimeException> {

    private static final Logger logger = LoggerFactory.getLogger(QueryProductAppointmentInfoTask.class);
   
    private QueryHighProductOuterService queryHighProductOuterService;

    private ProductAppointmentInfoBean productAppointmentInfoBean;
    
    private Date appDtm;
    
    private String busiType;

    private CountDownLatch latch;


    public QueryProductAppointmentInfoTask(QueryHighProductOuterService queryHighProductOuterService, ProductAppointmentInfoBean productAppointmentInfoBean, 
            Date appDtm, String busiType, CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.productAppointmentInfoBean = productAppointmentInfoBean;
        this.appDtm = appDtm;
        this.busiType = busiType;
        this.latch = latch;
        
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            ProductAppointmentInfoBean bean = queryHighProductOuterService.queryAppointmentInfo(productAppointmentInfoBean.getProductId(), productAppointmentInfoBean.getShareClass(), productAppointmentInfoBean.getDisCode(), appDtm, busiType);
            if (bean == null) {
                return null;
            }
            
            BeanUtils.copyProperties(bean, productAppointmentInfoBean);
        }catch(RuntimeException ex){
            logger.error("QueryProductAppointmentInfoTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

