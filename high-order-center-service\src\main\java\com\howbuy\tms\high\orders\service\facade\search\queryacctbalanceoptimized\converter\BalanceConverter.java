/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.converter;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.constants.BalanceConstants;

import java.util.List;

/**
 * @description: 持仓数据转换工具类
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
public class BalanceConverter {

    /**
     * @description: 将代销持仓数据转换为优化后的持仓Bean
     * @param balanceVo 代销持仓数据
     * @param productBean 产品信息
     * @param disCodeList 分销机构列表
     * @return 优化后的持仓Bean
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public static OptimizedBalanceBean convertConsignmentBalance(BalanceVo balanceVo, HighProductDBInfoBean productBean,
                                                                 List<String> disCodeList) {
        OptimizedBalanceBean bean = new OptimizedBalanceBean();
        
        // 基本信息
        bean.setDisCode(balanceVo.getDisCode());
        bean.setDisCodeList(disCodeList);
        bean.setProductCode(balanceVo.getProductCode());
        bean.setProductName(productBean.getFundAttr());
        bean.setProductType(productBean.getFundType());
        bean.setProductSubType(productBean.getFundSubType());
        bean.setCurrency(productBean.getCurrency());
        bean.setScaleType(BalanceConstants.ScaleType.CONSIGNMENT);
        bean.setHkSaleFlag(productBean.getHkSaleFlag());
        
        // 份额和金额信息
        bean.setBalanceVol(balanceVo.getBalanceVol());
        bean.setUnconfirmedVol(balanceVo.getUnconfirmedVol());
        bean.setUnconfirmedAmt(balanceVo.getUnconfirmedAmt());
        
        // 产品特殊标识
        bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        
        return bean;
    }

    /**
     * @description: 将直销持仓数据转换为优化后的持仓Bean
     * @param balanceVo 直销持仓数据
     * @param productBean 产品信息
     * @param disCodeList 分销机构列表
     * @return 优化后的持仓Bean
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public static OptimizedBalanceBean convertDirectBalance(BalanceVo balanceVo, HighProductDBInfoBean productBean,
                                                            List<String> disCodeList) {
        OptimizedBalanceBean bean = new OptimizedBalanceBean();
        
        // 基本信息
        bean.setDisCode(balanceVo.getDisCode());
        bean.setDisCodeList(disCodeList);
        bean.setProductCode(balanceVo.getProductCode());
        bean.setProductName(productBean.getFundAttr());
        bean.setProductType(productBean.getFundType());
        bean.setProductSubType(productBean.getFundSubType());
        bean.setCurrency(productBean.getCurrency());
        bean.setScaleType(BalanceConstants.ScaleType.DIRECT);
        bean.setHkSaleFlag(productBean.getHkSaleFlag());
        
        // 份额和金额信息
        bean.setBalanceVol(balanceVo.getBalanceVol());
        bean.setUnconfirmedVol(balanceVo.getUnconfirmedVol());
        bean.setUnconfirmedAmt(balanceVo.getUnconfirmedAmt());
        
        // 产品特殊标识
        bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        
        return bean;
    }

    /**
     * @description: 设置持仓Bean的净值相关信息
     * @param bean 持仓Bean
     * @param nav 净值
     * @param navDt 净值日期
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public static void setNavInfo(OptimizedBalanceBean bean, java.math.BigDecimal nav, String navDt) {
        bean.setNav(nav);
        bean.setNavDt(navDt);
    }

    /**
     * @description: 设置持仓Bean的收益相关信息
     * @param bean 持仓Bean
     * @param currentIncome 当前收益
     * @param currentIncomeRmb 当前收益(人民币)
     * @param accumIncome 累计收益
     * @param accumIncomeRmb 累计收益(人民币)
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public static void setIncomeInfo(OptimizedBalanceBean bean, java.math.BigDecimal currentIncome,
                                     java.math.BigDecimal currentIncomeRmb, java.math.BigDecimal accumIncome,
                                     java.math.BigDecimal accumIncomeRmb) {
        bean.setCurrentIncome(currentIncome);
        bean.setCurrentIncomeRmb(currentIncomeRmb);
        bean.setAccumIncome(accumIncome);
        bean.setAccumIncomeRmb(accumIncomeRmb);
    }

    /**
     * @description: 设置持仓Bean的成本相关信息
     * @param bean 持仓Bean
     * @param accumCost 累计成本
     * @param accumCostRmb 累计成本(人民币)
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public static void setCostInfo(OptimizedBalanceBean bean, java.math.BigDecimal accumCost,
                                   java.math.BigDecimal accumCostRmb) {
        bean.setAccumCost(accumCost);
        bean.setAccumCostRmb(accumCostRmb);
    }

    /**
     * @description: 设置持仓Bean的回款相关信息
     * @param bean 持仓Bean
     * @param accumCollection 累计回款
     * @param accumCollectionRmb 累计回款(人民币)
     * @param cashCollection 回款金额
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public static void setCollectionInfo(OptimizedBalanceBean bean, java.math.BigDecimal accumCollection,
                                         java.math.BigDecimal accumCollectionRmb, java.math.BigDecimal cashCollection) {
        bean.setAccumCollection(accumCollection);
        bean.setAccumCollectionRmb(accumCollectionRmb);
        bean.setCashCollection(cashCollection);
    }
}
