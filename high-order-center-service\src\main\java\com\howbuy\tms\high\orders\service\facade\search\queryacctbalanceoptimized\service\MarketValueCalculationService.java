/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.service;

import com.howbuy.tms.common.enums.busi.NavDisclosureTypeEnum;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlLatestAckVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.constants.BalanceConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 市值计算服务
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
@Service
public class MarketValueCalculationService {

    private static final Logger logger = LogManager.getLogger(MarketValueCalculationService.class);

    /**
     * @description: 计算市值
     * @param balanceBean 持仓Bean
     * @param navBean 净值Bean
     * @param latestAckList 最新确认订单列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public void calculateMarketValue(OptimizedBalanceBean balanceBean, HighProductNavBean navBean, 
                                     List<HighDealOrderDtlLatestAckVo> latestAckList) {
        
        BigDecimal nav = null;
        String navDate = null;
        
        if (navBean == null || navBean.getNav() == null) {
            // 特殊产品指标控制需求：20221122
            // 产品范围：固定收益类产品，即【产品大类productSubType】=2-固定收益
            // （2）若基金的【净值披露方式】=2-份额收益：【持仓总市值】=【持仓份额累计】* 1；
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                    && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
                nav = BigDecimal.ONE;
            } else {
                return;
            }
        } else {
            // 特殊产品指标控制需求：20221122
            // 产品范围：固定收益类产品，即【产品大类productSubType】=2-固定收益
            // （2）若基金的【净值披露方式】=2-份额收益：【持仓总市值】=【持仓份额累计】* 1；
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                    && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType())) {
                nav = BigDecimal.ONE;
            } else {
                nav = navBean.getNav();
                navDate = navBean.getNavDate();
            }
        }
        
        // 设置净值信息
        balanceBean.setNav(MoneyUtil.formatMoney(nav, 4));
        balanceBean.setNavDt(navDate);
        
        // 计算市值
        if (CollectionUtils.isEmpty(latestAckList) || StringUtils.isEmpty(navDate)) {
            // 简单计算：市值 = 持仓份额 * 净值
            BigDecimal marketValue = balanceBean.getBalanceVol().multiply(nav);
            balanceBean.setMarketValue(MoneyUtil.formatMoney(marketValue, BalanceConstants.Numbers.MONEY_SCALE));
            balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(marketValue, BalanceConstants.Numbers.MONEY_SCALE));
        } else {
            // 复杂计算：考虑最新确认订单
            calculateMarketValueWithLatestAck(balanceBean, nav, navDate, latestAckList);
        }
        
        // 股权产品特殊处理：市值使用净购买金额
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            if (balanceBean.getNetBuyAmount() != null) {
                balanceBean.setMarketValue(balanceBean.getNetBuyAmount());
                balanceBean.setCurrencyMarketValue(balanceBean.getCurrencyNetBuyAmount());
            }
        }
    }

    /**
     * @description: 考虑最新确认订单计算市值
     * @param balanceBean 持仓Bean
     * @param nav 净值
     * @param navDate 净值日期
     * @param latestAckList 最新确认订单列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private void calculateMarketValueWithLatestAck(OptimizedBalanceBean balanceBean, BigDecimal nav, String navDate,
                                                   List<HighDealOrderDtlLatestAckVo> latestAckList) {
        
        BigDecimal ackNetAmt = BigDecimal.ZERO;
        BigDecimal ackVol = BigDecimal.ZERO;
        
        for (HighDealOrderDtlLatestAckVo vo : latestAckList) {
            // 净值日期小于等于上报日期，市值=一笔或多笔交易的确认金额（不含费）+{持有份额-（一笔或多笔交易合计确认份额）}*DB最新净值
            if (navDate.compareTo(vo.getSubmitTaDt()) <= 0) {
                ackNetAmt = ackNetAmt.add(vo.getAckAmt().subtract(vo.getFee()));
                ackVol = ackVol.add(vo.getAckVol());
            }
        }
        
        // 市值=一笔或多笔交易的确认金额（不含费）+{持有份额-（一笔或多笔交易合计确认份额）}*DB最新净值
        BigDecimal marketValue = ackNetAmt.add(balanceBean.getBalanceVol().subtract(ackVol).multiply(nav));
        
        balanceBean.setMarketValue(MoneyUtil.formatMoney(marketValue, BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(marketValue, BalanceConstants.Numbers.MONEY_SCALE));
    }

    /**
     * @description: 计算千禧产品市值(需要加上待投金额)
     * @param balanceBean 持仓Bean
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public void calculateQianXiMarketValue(OptimizedBalanceBean balanceBean) {
        if (BalanceConstants.ProductFlags.QIAN_XI_FLAG.equals(balanceBean.getQianXiFlag()) 
                && balanceBean.getUnPaidInAmt() != null 
                && balanceBean.isBalance()) {
            
            BigDecimal currentMarketValue = balanceBean.getMarketValue() != null ? 
                    balanceBean.getMarketValue() : BigDecimal.ZERO;
            BigDecimal totalMarketValue = currentMarketValue.add(balanceBean.getUnPaidInAmt());
            
            balanceBean.setMarketValue(MoneyUtil.formatMoney(totalMarketValue, BalanceConstants.Numbers.MONEY_SCALE));
            balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(totalMarketValue, BalanceConstants.Numbers.MONEY_SCALE));
        }
    }

    /**
     * @description: 处理NA产品费用
     * @param balanceBean 持仓Bean
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public void processNAProductFee(OptimizedBalanceBean balanceBean) {
        if ("1".equals(balanceBean.getNaProductFeeType()) && 
                balanceBean.getReceivManageFee() != null && 
                balanceBean.getReceivPreformFee() != null) {
            
            // 当前币种NA费用
            BigDecimal naFee = balanceBean.getReceivManageFee().add(balanceBean.getReceivPreformFee());
            
            if (balanceBean.getCurrencyMarketValue() != null) {
                BigDecimal marketValueExFee = balanceBean.getCurrencyMarketValue().subtract(naFee);
                balanceBean.setCurrencyMarketValueExFee(MoneyUtil.formatMoney(marketValueExFee, BalanceConstants.Numbers.MONEY_SCALE));
            }
            
            if (balanceBean.getMarketValue() != null) {
                BigDecimal marketValueExFee = balanceBean.getMarketValue().subtract(naFee);
                balanceBean.setMarketValueExFee(MoneyUtil.formatMoney(marketValueExFee, BalanceConstants.Numbers.MONEY_SCALE));
            }
        }
    }
}
