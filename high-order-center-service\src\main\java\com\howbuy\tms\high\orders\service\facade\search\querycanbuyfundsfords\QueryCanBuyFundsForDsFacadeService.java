/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.facade.search.querycanbuyfundsfords;

import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.interlayer.product.model.HighProductInfoPageModel;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.datasource.RouteHolder;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsFacade;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsRequest;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsResponse;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.bean.CanBuyFunds;
import com.howbuy.tms.high.orders.service.business.task.QueryCanBuyFundsForDsTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description: (查询管理人可购买的产品列表)
 * @date 2022/5/5 16:27
 * @since JDK 1.8
 */
@DubboService
@Service("queryCanBuyFundsForDsFacade")
public class QueryCanBuyFundsForDsFacadeService implements QueryCanBuyFundsForDsFacade {
    private static final Logger logger = LogManager.getLogger(QueryCanBuyFundsForDsFacadeService.class);
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    private static int MAXTHREADCOUNT = 5;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsFacade.execute(QueryCanBuyFundsForDsRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryCanBuyFundsForDsFacadeService
     * @apiName execute
     * @apiDescription 查询管理人可购买的产品列表
     * @apiParam (请求参数) {Array} fundManCodeList 产品管理人代码
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=2S11pgGS1X&pageSize=7581&disCode=97XSWaDe&txChannel=QARdF8i0&appTm=TnC&subOutletCode=OzoIR4X&pageNo=7754&operIp=lMEWciYzN&txAcctNo=beG&fundManCodeList=IrCer3&appDt=zvMI&dataTrack=afc&txCode=gJiYVadg&outletCode=7X2yv9NLV
     * @apiSuccess (响应结果) {Array} canBuyFundsList 管理人可购买产品列表
     * @apiSuccess (响应结果) {String} canBuyFundsList.fundManCode 管理人代码
     * @apiSuccess (响应结果) {Array} canBuyFundsList.fundInfos 可购买产品列表
     * @apiSuccess (响应结果) {String} canBuyFundsList.fundInfos.FundCode
     * @apiSuccess (响应结果) {String} canBuyFundsList.fundInfos.FundName
     * @apiSuccess (响应结果) {Number} canBuyFundsList.totalCount 总记录数
     * @apiSuccess (响应结果) {Number} canBuyFundsList.totalPage 总页数
     * @apiSuccess (响应结果) {Number} canBuyFundsList.pageNo 当前页
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"tbv","canBuyFundsList":[{"fundManCode":"aF","totalPage":4993,"pageNo":488,"fundInfos":[{"FundCode":"FNn0h","FundName":"7kCl7lx1lb"}],"totalCount":1495}],"totalPage":5944,"pageNo":8658,"description":"3KiZQZ","totalCount":9696}
     */
    @Override
    public <T> QueryCanBuyFundsForDsResponse execute(QueryCanBuyFundsForDsRequest request) {
        logger.info("QueryCanBuyFundsForDsFacadeService start");
        QueryCanBuyFundsForDsResponse response = new QueryCanBuyFundsForDsResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        if (CollectionUtils.isEmpty(request.getFundManCodeList())) {
            return response;
        }

        // 结果数据
        List<CanBuyFunds> list = new ArrayList<>();
        response.setCanBuyFundsList(list);
        CanBuyFunds bean = null;
        for (String fundManCode : request.getFundManCodeList()) {
            bean = new CanBuyFunds();
            bean.setFundManCode(fundManCode);
            list.add(bean);
        }

        // 获取交易日
        String taTradeDt = queryTradeDayOuterService.getWorkDay(request.getAppDt(), request.getAppTm());

        // 根据管理人代码查询产品列表
        for (CanBuyFunds canBuyFunds : list) {
            List<CanBuyFunds.FundInfo> fundInfos = new CopyOnWriteArrayList<>();
            canBuyFunds.setFundInfos(fundInfos);
            HighProductInfoPageModel highProductInfoPageModel = queryHighProductOuterService.getProductListByFundManCode(canBuyFunds.getFundManCode(), request.getPageNo(), request.getPageSize());
            canBuyFunds.setPageNo(highProductInfoPageModel.getPageNum());
            canBuyFunds.setTotalPage(highProductInfoPageModel.getPages());
            canBuyFunds.setTotalCount(highProductInfoPageModel.getTotal());
            List<HighProductInfoModel> highProductInfoModelList = highProductInfoPageModel.getHighProductInfoModelList();
            if (CollectionUtils.isEmpty(highProductInfoModelList)) {
                continue;
            }
            // 处理
            process(request, fundInfos, taTradeDt, highProductInfoModelList);
        }

        logger.info("QueryCanBuyFundsForDsFacadeService end");
        return response;
    }

    private void process(QueryCanBuyFundsForDsRequest request, List<CanBuyFunds.FundInfo> fundInfos, String taTradeDt, List<HighProductInfoModel> highProductInfoModelList) {
        // 获取数据源
        CountDownLatch latch = null;
        String ordersKey = RouteHolder.getRouteKey();
        try {
            int divs = highProductInfoModelList.size() / MAXTHREADCOUNT;
            int mods = highProductInfoModelList.size() % MAXTHREADCOUNT;

            if (divs > 0) {
                latch = new CountDownLatch(MAXTHREADCOUNT);
                List<HighProductInfoModel> subList = null;
                for (int i = 0; i < MAXTHREADCOUNT; i++) {
                    if (i == (MAXTHREADCOUNT - 1)) {
                        subList = highProductInfoModelList.subList(i * divs, highProductInfoModelList.size());
                    } else {
                        subList = highProductInfoModelList.subList(i * divs, (i + 1) * divs);
                    }
                    CommonThreadPool.submit(new QueryCanBuyFundsForDsTask(queryHighProductOuterService,
                            subList, request, taTradeDt, latch, ordersKey, fundInfos));
                }
            } else if (mods > 0) {
                latch = new CountDownLatch(1);
                CommonThreadPool.submit(new QueryCanBuyFundsForDsTask(queryHighProductOuterService,
                        highProductInfoModelList, request, taTradeDt, latch, ordersKey, fundInfos));
            }
        } catch (Exception e) {
            logger.error("QueryCanBuyFundsForDsFacadeService|process|error, msg:{}", e.getMessage(), e);
        } finally {
            try {
                if (latch != null) {
                    latch.await();
                }
            } catch (InterruptedException e) {
                logger.error("QueryCanBuyFundsForDsFacadeService|process|latch.await error, msg:{}", e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }

    }
}