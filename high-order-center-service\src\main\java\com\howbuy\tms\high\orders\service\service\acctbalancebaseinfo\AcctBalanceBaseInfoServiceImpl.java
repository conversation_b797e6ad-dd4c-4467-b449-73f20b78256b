package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.EffectiveType;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.common.Constants;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.BusinessCodeTransferConfigInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.dao.vo.CustBooksDtlVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import com.howbuy.tms.high.orders.dao.vo.SubCustBooksVo;
import com.howbuy.tms.high.orders.service.business.supplesubs.SuppleSubsService;
import com.howbuy.tms.high.orders.service.business.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.repository.CmCustFundDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CmCusttradeDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksDtlRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceDetailBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctSubBalanceDetailInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.ConsignmentOrderInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.DirectOrderInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.OwnershipOrderInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.ProductOrderInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceDetailBaseInfoParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctSubBalanceDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Description:用户持仓基础信息接口
 * @Author: yun.lu
 * Date: 2023/8/16 14:06
 */
@Service
@Slf4j
public class AcctBalanceBaseInfoServiceImpl implements AcctBalanceBaseInfoService {
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private CmCustFundDirectRepository cmCustFundDirectRepository;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private SubCustBooksRepository subCustBooksRepository;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;
    @Autowired
    private CustBooksDtlRepository custBooksDtlRepository;
    @Autowired
    public SuppleSubsService suppleSubsService;

    /**
     * 查询股权订单信息集合
     */
    @Override
    public List<OwnershipOrderInfo> queryAcctOwnershipOrderInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam) {
        // 0.参数校验与赋值
        checkAndBuildParam(queryAcctBalanceBaseParam);
        // 1.查询订单信息
        // 1.1.代销
        List<BalanceOrderVo> balanceConsignmentOrderVoList = new ArrayList<>();
        if (StringUtils.isNotBlank(queryAcctBalanceBaseParam.getTxAcctNo())) {
            balanceConsignmentOrderVoList = highDealOrderDtlRepository.selectBalanceConsignmentOrderVo(queryAcctBalanceBaseParam.getTxAcctNo(), queryAcctBalanceBaseParam.getFundCodeList(), queryAcctBalanceBaseParam.getDisCodeList());
        }
        // 1.2.直销
        List<BalanceOrderVo> balanceDirectOrderVoList = new ArrayList<>();
        if (StringUtils.isNotBlank(queryAcctBalanceBaseParam.getHbOneNo())) {
            balanceDirectOrderVoList = cmCusttradeDirectRepository.selectBalanceDirectOrderVo(queryAcctBalanceBaseParam.getHbOneNo(), queryAcctBalanceBaseParam.getFundCodeList(), queryAcctBalanceBaseParam.getDisCodeList());
        }
        // 将直销的业务.类型转换为中台,方便后面统一处理逻辑
        if (CollectionUtils.isNotEmpty(balanceDirectOrderVoList)) {
            balanceDirectOrderVoList.forEach(order -> {
                // 类型转换
                String mBusinessCode = "1" + order.getBusinessCode();
                order.setMBusinessCode(mBusinessCode);
            });
        }
        // 1.3.合并订单信息
        List<BalanceOrderVo> balanceOrderVoList = new ArrayList<>();
        balanceOrderVoList.addAll(balanceConsignmentOrderVoList);
        balanceOrderVoList.addAll(balanceDirectOrderVoList);
        if (CollectionUtils.isEmpty(balanceOrderVoList)) {
            log.info("queryAcctOwnershipOrderInfo，根据条件没有查询出任何订单,queryAcctTradeInfoParam={}", queryAcctBalanceBaseParam);
            return new ArrayList<>();
        }
        // 2.判断首单
        // 2.1.按照交易日期排序
        balanceOrderVoList = balanceOrderVoList.stream().sorted(Comparator.comparing(BalanceOrderVo::getTradeDt)).collect(Collectors.toList());
        // 2.2.判断首单
        Set<String> valueSet = new HashSet<>();
        balanceOrderVoList.forEach(order -> {
            // 判断首单
            String joinKey = String.join(Constants.UNDERLINE, order.getFundCode(), order.getMBusinessCode());
            if (valueSet.contains(joinKey)) {
                order.setFirstOrder(false);
            } else {
                valueSet.add(joinKey);
                order.setFirstOrder(true);
            }
        });
        // 4.构建订单信息实体
        List<OwnershipOrderInfo> orderList = new ArrayList<>();
        // 4.1.如果是直销
        balanceDirectOrderVoList.forEach(balanceOrderVo -> {
            // 获取转义后的业务code
            String transferMiddleBusinessCode = getBusinessTransferCode(balanceOrderVo.getOrderNo(), balanceOrderVo.getFundCode(), balanceOrderVo.getMBusinessCode(), balanceOrderVo.isFirstOrder(), null);
            orderList.add(new DirectOrderInfo(balanceOrderVo, transferMiddleBusinessCode));
        });
        // 4.2.如果是代销
        balanceConsignmentOrderVoList.forEach(balanceOrderVo -> {
            // 获取转义后的业务code
            String transferMiddleBusinessCode = getBusinessTransferCode(balanceOrderVo.getOrderNo(), balanceOrderVo.getFundCode(), balanceOrderVo.getMBusinessCode(), balanceOrderVo.isFirstOrder(), null);
            orderList.add(new ConsignmentOrderInfo(balanceOrderVo, transferMiddleBusinessCode));
        });
        return orderList;
    }

    @Override
    public List<AcctBalanceBaseInfo> queryAcctBalanceBaseInfo(QueryAcctBalanceBaseParam param) {

        List<AcctBalanceBaseInfo> balanceBaseInfoList = new ArrayList<>();
        // 1.查询代销持仓信息
        if (StringUtils.isNotBlank(param.getTxAcctNo())) {
            List<BalanceVo> proxyBalanceVoList = custBooksRepository.selectBalanceWithLockPeriod(param.getDisCodeList(), param.getTxAcctNo(), param.getFundCodeList(), YesOrNoEnum.YES.getCode());
            for (BalanceVo balanceVo : proxyBalanceVoList) {
                AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                acctBalanceBaseInfo.setBalanceVol(balanceVo.getBalanceVol());
                acctBalanceBaseInfo.setDisCode(balanceVo.getDisCode());
                acctBalanceBaseInfo.setFundCode(balanceVo.getProductCode());
                acctBalanceBaseInfo.setIsHkProduct(YesOrNoEnum.NO.getCode());
                acctBalanceBaseInfo.setTxAcctNo(param.getTxAcctNo());
                acctBalanceBaseInfo.setHbOneNo(param.getHbOneNo());
                balanceBaseInfoList.add(acctBalanceBaseInfo);
            }
        }
        // 2.查询出直销的持仓信息
        if (StringUtils.isNotBlank(param.getHbOneNo())  && param.isIncludeDirect()) {
            List<CmCustFundDirectPo> cmBalanceList = cmCustFundDirectRepository.selectDirectBalance(param.getHbOneNo(), YesOrNoEnum.YES.getCode(), param.getDisCodeList());
            for (CmCustFundDirectPo po : cmBalanceList) {
                AcctBalanceBaseInfo directAcctBalanceBaseInfo = getDirectAcctBalanceBaseInfo(param, po);
                if (CollectionUtil.isNotEmpty(param.getFundCodeList()) && !param.getFundCodeList().contains(directAcctBalanceBaseInfo.getFundCode())) {
                    continue;
                }
                balanceBaseInfoList.add(directAcctBalanceBaseInfo);
            }
        }
        return balanceBaseInfoList;
    }


    @Override
    public List<AcctSubBalanceDetailInfo> queryAcctSubBalanceDetailInfo(QueryAcctSubBalanceDetailParam param) {
        log.info("queryAcctSubBalanceDetailInfo-查询持仓明细,param={}", param);
        List<AcctSubBalanceDetailInfo> subBalanceDetailBaseInfoList = new ArrayList<>();
        // 1.查询产品基本信息
        HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(param.getFundCode());
        if (highProductBaseInfoBean == null) {
            log.info("queryAcctSubBalanceDetailInfo-查询持仓明细,根据产品Code查不到产品信息,fundCode:{}", param.getFundCode());
            return subBalanceDetailBaseInfoList;
        }
        // 2.查询用户持仓
        List<SubCustBooksVo> subCustBooksPoList = subCustBooksRepository.selectAcctSubCustBooks(param.getTxAcctNo(), param.getFundCode());
        if (CollectionUtils.isEmpty(subCustBooksPoList)) {
            log.info("queryAcctSubBalanceDetailInfo-查询持仓明细,根据入参查不到持仓,queryAcctBalanceDetailBaseInfoParam:{} ", param);
            return subBalanceDetailBaseInfoList;
        }
        subCustBooksPoList = subCustBooksPoList.stream().sorted(Comparator.comparing(SubCustBooksVo::getAckDt, Comparator.naturalOrder())).collect(Collectors.toList());
        // 2.2.查询赎回在途持仓
        List<CustBooksDtlVo> unConfirmRedeemBalanceVolList = custBooksDtlRepository.getUnConfirmRedeemBalanceVol(param.getTxAcctNo(), param.getFundCode());
        Map<String, CustBooksDtlVo> custBooksDtlMap = unConfirmRedeemBalanceVolList.stream().collect(Collectors.toMap(x -> String.join(Constants.UNDERLINE, x.getTxAcctNo(), x.getCpAcctNo(), x.getFundCode()), x -> x));
        // 3.计算可用份额,总份额
        String appDt = DateUtils.formatToString(param.getAppDtm(), DateUtils.YYYYMMDD);
        String appTm = DateUtils.formatToString(param.getAppDtm(), DateUtils.HHMMSS);
        String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(appDt, appTm);
        String redeemDt = getSubmitDt(highProductBaseInfoBean, param.getDisCode(), taTradeDt, param.getAppDtm());

        subCustBooksPoList.forEach(subCustBooksVo -> {
            AcctSubBalanceDetailInfo subBalanceDetailInfo = new AcctSubBalanceDetailInfo();
            subBalanceDetailInfo.setTxAcctNo(subCustBooksVo.getTxAcctNo());
            subBalanceDetailInfo.setFundCode(subCustBooksVo.getFundCode());
            subBalanceDetailInfo.setAckDt(subCustBooksVo.getAckDt());
            subBalanceDetailInfo.setCpAcctNo(subCustBooksVo.getCpAcctNo());
            subBalanceDetailInfo.setOpenRedeemDt(subCustBooksVo.getOpenRedeemDt());
            // 持仓份额
            subBalanceDetailInfo.setBalanceVol(subCustBooksVo.getBalanceVol());
            // 设置在途赎回
            setRedeemUnConfirmedVol(custBooksDtlMap, subCustBooksVo);
            // 锁定份额->如果没有到赎回开放日,可用份额就是0
            if (redeemDt != null && subCustBooksVo.getOpenRedeemDt() != null && redeemDt.compareTo(subCustBooksVo.getOpenRedeemDt()) < 0) {
                subBalanceDetailInfo.setAvailVol(BigDecimal.ZERO);
            } else {
                // 可用份额=份额-在途-冻结
                BigDecimal availVol = subCustBooksVo.getBalanceVol().subtract(subCustBooksVo.getRedeemUnconfirmedVol()).subtract(subCustBooksVo.getFrznVol()).subtract(subCustBooksVo.getJustFrznVol());
                subBalanceDetailInfo.setAvailVol(BigDecimal.ZERO.compareTo(availVol) < 0 ? availVol : BigDecimal.ZERO);
            }
            subBalanceDetailBaseInfoList.add(subBalanceDetailInfo);
        });

        // 4.获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<String, QueryCustBankCardResult>();
        CountDownLatch latch = new CountDownLatch(subBalanceDetailBaseInfoList.size());
        for (AcctSubBalanceDetailInfo info : subBalanceDetailBaseInfoList) {
            String cpAcct = info.getCpAcctNo();
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, param.getTxAcctNo(), param.getDisCode(), cpAcct, bankCardInfo, param.getOutletCode()));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("queryAcctSubBalanceDetailInfo-查询持仓明细,获取银行卡信息异常:" + e);
            Thread.currentThread().interrupt();
        }
        // 5.银行卡信息返回
        for (AcctSubBalanceDetailInfo book : subBalanceDetailBaseInfoList) {
            // 银行卡信息
            QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
            if (result != null) {
                book.setBankAcct(result.getBankAcct());
                book.setBankName(result.getBankName());
                book.setBankAcctMask(result.getBankAcctMask());
                book.setBankCode(result.getBankCode());
            }
        }
        log.info("queryAcctSubBalanceDetailInfo-获取持仓明细,subBalanceDetailBaseInfoList={}", subBalanceDetailBaseInfoList);
        return subBalanceDetailBaseInfoList;
    }

    @Override
    public List<AcctBalanceBaseInfo> queryOnWayBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam) {
        checkAndBuildParam(queryAcctBalanceBaseInfoParam);
        List<AcctBalanceBaseInfo> acctBalanceBaseInfoList = new ArrayList<>();
        // 1.查询代销在途
        List<CustBooksDtlVo> onWayBuyBalanceVoList = custBooksDtlRepository.getOnWayBuyBalanceVoList(queryAcctBalanceBaseInfoParam.getTxAcctNo(), queryAcctBalanceBaseInfoParam.getFundCodeList(), queryAcctBalanceBaseInfoParam.getDisCodeList());
        if (CollectionUtils.isNotEmpty(onWayBuyBalanceVoList)) {
            for (CustBooksDtlVo custBooksDtlVo : onWayBuyBalanceVoList) {
                AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                acctBalanceBaseInfo.setBalanceVol(custBooksDtlVo.getUnconfirmedVol());
                acctBalanceBaseInfo.setTxAcctNo(custBooksDtlVo.getTxAcctNo());
                acctBalanceBaseInfo.setFundCode(custBooksDtlVo.getFundCode());
                acctBalanceBaseInfo.setDisCode(custBooksDtlVo.getDisCode());
                acctBalanceBaseInfoList.add(acctBalanceBaseInfo);
            }
        }
        // 2.查询直销在途
        if (queryAcctBalanceBaseInfoParam.isIncludeDirect()) {
            QueryAcctBalanceBaseInfoParamVo paramVo = new QueryAcctBalanceBaseInfoParamVo();
            BeanUtils.copyProperties(queryAcctBalanceBaseInfoParam, paramVo);
            List<CmCusttradeDirectPo> onWayDirectBalanceList = cmCusttradeDirectRepository.getOnWayDirectBalance(paramVo);
            if (CollectionUtils.isNotEmpty(onWayDirectBalanceList)) {
                Map<String, List<CmCusttradeDirectPo>> cmCustTradeDirectPoMap = onWayDirectBalanceList.stream().collect(Collectors.groupingBy(x -> x.getFundcode() + Constants.UNDERLINE + x.getDiscode() + x.getTxAcctNo()));
                cmCustTradeDirectPoMap.forEach((k, subList) -> {
                    BigDecimal totalVol = subList.stream().map(CmCusttradeDirectPo::getAppvol).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                    acctBalanceBaseInfo.setBalanceVol(totalVol);
                    acctBalanceBaseInfo.setTxAcctNo(subList.get(0).getTxAcctNo());
                    acctBalanceBaseInfo.setIsHkProduct(subList.get(0).getIsHkProduct());
                    acctBalanceBaseInfo.setFundCode(subList.get(0).getFundcode());
                    acctBalanceBaseInfo.setDisCode(subList.get(0).getDiscode());
                    acctBalanceBaseInfoList.add(acctBalanceBaseInfo);
                });
            }
        }
        return acctBalanceBaseInfoList;
    }

    @Override
    public List<AcctBalanceBaseInfo> queryConfirmBalanceBaseInfo(QueryAcctBalanceBaseParam param) {
        log.info("queryConfirmBalanceBaseInfo-查询确认持仓,param={}", JSON.toJSONString(param));
        // 1.参数校验
        checkAndBuildParam(param);
        List<AcctBalanceBaseInfo> balanceList = new ArrayList<>();
        // 2.查询代销确认持仓
        QueryAcctBalanceBaseInfoParamVo paramVo = new QueryAcctBalanceBaseInfoParamVo();
        BeanUtils.copyProperties(param, paramVo);
        List<BalanceVo> agentBalanceVoList = custBooksRepository.queryConfirmBalanceBaseInfo(paramVo);
        if (CollectionUtils.isNotEmpty(agentBalanceVoList)) {
            List<AcctBalanceBaseInfo> agentBalanceList = agentBalanceVoList.stream().map(balanceVo -> {
                AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
                acctBalanceBaseInfo.setBalanceVol(balanceVo.getBalanceVol());
                acctBalanceBaseInfo.setDisCode(balanceVo.getDisCode());
                acctBalanceBaseInfo.setFundCode(balanceVo.getProductCode());
                acctBalanceBaseInfo.setIsHkProduct(YesOrNoEnum.NO.getCode());
                acctBalanceBaseInfo.setTxAcctNo(param.getTxAcctNo());
                acctBalanceBaseInfo.setHbOneNo(param.getHbOneNo());
                return acctBalanceBaseInfo;

            }).collect(Collectors.toList());
            balanceList.addAll(agentBalanceList);
        }
        // 2.查询直销确认持仓
        if (param.isIncludeDirect()) {
            QueryAcctBalanceBaseInfoParamVo queryAcctBalanceBaseInfoParamVo = new QueryAcctBalanceBaseInfoParamVo();
            BeanUtils.copyProperties(param, queryAcctBalanceBaseInfoParamVo);
            List<CmCustFundDirectPo> cmBalanceList = cmCustFundDirectRepository.queryConfirmBalanceBaseInfo(queryAcctBalanceBaseInfoParamVo);
            if (CollectionUtils.isNotEmpty(cmBalanceList)) {
                List<AcctBalanceBaseInfo> directBalanceInfoList = cmBalanceList.stream().map(po -> getDirectAcctBalanceBaseInfo(param, po)).collect(Collectors.toList());
                balanceList.addAll(directBalanceInfoList);
            }
        }
        return balanceList;
    }

    /**
     * 直销份额转基础信息实体
     */
    private AcctBalanceBaseInfo getDirectAcctBalanceBaseInfo(QueryAcctBalanceBaseParam param, CmCustFundDirectPo po) {
        AcctBalanceBaseInfo acctBalanceBaseInfo = new AcctBalanceBaseInfo();
        acctBalanceBaseInfo.setBalanceVol(po.getBalanceVol());
        acctBalanceBaseInfo.setDisCode(po.getDisCode());
        // 分期成立的产品,直销mjjdm记录的是主产品代码,fundCode是子产品
        if (StringUtils.isNotEmpty(po.getMjjDm())) {
            acctBalanceBaseInfo.setFundCode(po.getMjjDm());
        } else {
            acctBalanceBaseInfo.setFundCode(po.getFundCode());
        }
        acctBalanceBaseInfo.setIsHkProduct(po.getIsHkProduct());
        acctBalanceBaseInfo.setTxAcctNo(param.getTxAcctNo());
        acctBalanceBaseInfo.setHbOneNo(param.getHbOneNo());
        return acctBalanceBaseInfo;
    }

    /**
     * 设置在途份额
     */
    private void setRedeemUnConfirmedVol(Map<String, CustBooksDtlVo> custBooksDtlMap, SubCustBooksVo subCustBooksVo) {
        //在途份额
        CustBooksDtlVo custBooksDtlVo = custBooksDtlMap.get(String.join(Constants.UNDERLINE, subCustBooksVo.getTxAcctNo(), subCustBooksVo.getCpAcctNo(), subCustBooksVo.getFundCode()));
        // 1.没有在途,就直接是0
        if (custBooksDtlVo == null) {
            subCustBooksVo.setRedeemUnconfirmedVol(BigDecimal.ZERO);
            return;
        }
        BigDecimal unconfirmedVol = custBooksDtlVo.getUnconfirmedVol();
        if (unconfirmedVol == null || BigDecimal.ZERO.compareTo(unconfirmedVol) >= 0) {
            subCustBooksVo.setRedeemUnconfirmedVol(BigDecimal.ZERO);
            return;
        }

        BigDecimal unFrznVol = subCustBooksVo.getBalanceVol().subtract(subCustBooksVo.getFrznVol()).subtract(subCustBooksVo.getJustFrznVol());
        // 2.如果在途的大于未冻结的份额,当前未冻结持仓全部赎回在途
        if (unconfirmedVol.compareTo(unFrznVol) >= 0) {
            subCustBooksVo.setRedeemUnconfirmedVol(unFrznVol);
            custBooksDtlVo.setUnconfirmedVol(unconfirmedVol.subtract(unFrznVol));
        } else {
            // 3.小于未冻结持仓,在途的就是所有在途赎回
            subCustBooksVo.setRedeemUnconfirmedVol(unconfirmedVol);
            custBooksDtlVo.setUnconfirmedVol(BigDecimal.ZERO);
        }
    }

    @Override
    public List<AcctBalanceDetailBaseInfo> queryAcctBalanceDetailBaseInfo(QueryAcctBalanceDetailBaseInfoParam param) {
        log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,queryAcctBalanceDetailBaseInfoParam={}", param);
        List<AcctBalanceDetailBaseInfo> balanceDetailBaseInfoList = new ArrayList<>();
        // 1.查询产品基本信息
        HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(param.getFundCode());
        if (highProductBaseInfoBean == null) {
            log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,根据产品Code查不到产品信息,fundCode:{}", param.getFundCode());
            return balanceDetailBaseInfoList;
        }
        // 2.获取上报日
        String submitDt = getSubmitDt(highProductBaseInfoBean, param.getDisCodeList().get(0), param.getTradeDt(), param.getAppDate());
        log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,submitDt:{},queryAcctBalanceDetailBaseInfoParam:{} ", submitDt, param);

        // 3.查询用户持仓
        List<BalanceVo> booklist = custBooksRepository.selectBalanceDtlByDisCodeList(param.getDisCodeList(), param.getTxAcctNo(), null, param.getFundCode(), param.getCpAcctNo(), submitDt);
        if (CollectionUtils.isEmpty(booklist)) {
            log.info("queryAcctBalanceDetailBaseInfo-查询持仓明细,根据入参查不到持仓,submitDt={},queryAcctBalanceDetailBaseInfoParam:{} ", submitDt, param);
            return balanceDetailBaseInfoList;
        }
        // 4.获取可用份额
        Set<String> cpAcctNoSet = new HashSet<>();
        Iterator<BalanceVo> bookListIterator = booklist.iterator();
        while (bookListIterator.hasNext()) {
            BalanceVo book = bookListIterator.next();
            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getUnconfirmedVol()).subtract(book.getJustFrznVol());
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                availVol = availVol.subtract(book.getLockingPeriodVol());
            }
            if (availVol.compareTo(BigDecimal.ZERO) <= 0) {
                bookListIterator.remove();
            } else {
                cpAcctNoSet.add(book.getCpAcctNo());
            }
        }

        // 5.获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<String, QueryCustBankCardResult>();
        CountDownLatch latch = new CountDownLatch(cpAcctNoSet.size());
        for (Object value : cpAcctNoSet.toArray()) {
            String cpAcct = (String) value;
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, param.getTxAcctNo(), param.getDisCodeList().get(0), cpAcct, bankCardInfo, param.getTxId()));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("queryAcctBalanceDetailBaseInfo-查询持仓明细,获取银行卡信息异常:" + e);
            Thread.currentThread().interrupt();
        }

        List<AcctBalanceDetailBaseInfo> baseInfoList = new ArrayList<>();
        setBalanceInfo(param, highProductBaseInfoBean, booklist, bankCardMap, baseInfoList);
        return baseInfoList;
    }

    /**
     * 设置持仓信息
     */
    private void setBalanceInfo(QueryAcctBalanceDetailBaseInfoParam param, HighProductBaseInfoBean highProductBaseInfoBean,
                                List<BalanceVo> booklist, Map<String, QueryCustBankCardResult> bankCardMap, List<AcctBalanceDetailBaseInfo> baseInfoList) {
        BigDecimal totalVol = BigDecimal.ZERO;
        BigDecimal totalUnConfirmVol = BigDecimal.ZERO;
        BigDecimal totalAvailVol = BigDecimal.ZERO;
        BigDecimal totalRedeemAllSurplusVol = BigDecimal.ZERO;
        for (BalanceVo book : booklist) {
            AcctBalanceDetailBaseInfo baseInfo = new AcctBalanceDetailBaseInfo();
            baseInfo.setTxAcctNo(param.getTxAcctNo());
            baseInfo.setDisCode(param.getDisCodeList().get(0));
            baseInfo.setProductCode(book.getProductCode());
            baseInfo.setCpAcctNo(book.getCpAcctNo());
            baseInfo.setProtocolNo(book.getProtocolNo());
            totalVol = MathUtils.add(book.getBalanceVol(), totalVol);
            totalUnConfirmVol = MathUtils.add(book.getUnconfirmedVol(), totalUnConfirmVol);
            totalUnConfirmVol = MathUtils.add(totalUnConfirmVol, book.getJustFrznVol());
            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getUnconfirmedVol()).subtract(book.getJustFrznVol());
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                availVol = availVol.subtract(book.getLockingPeriodVol());
            }
            baseInfo.setAvailVol(MoneyUtil.formatMoney(availVol, 2));
            totalAvailVol = MathUtils.add(totalAvailVol, availVol);
            // 全赎剩余份额 = 未到锁定期 + 司法冻结
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                baseInfo.setRedeemAllSurplusVol(MathUtils.add(book.getLockingPeriodVol(), book.getJustFrznVol()));
            } else {
                baseInfo.setRedeemAllSurplusVol(book.getJustFrznVol());
            }
            totalRedeemAllSurplusVol = MathUtils.add(totalRedeemAllSurplusVol, baseInfo.getRedeemAllSurplusVol());
            // 基金信息
            baseInfo.setProductName(highProductBaseInfoBean.getFundAttr());
            baseInfo.setProductType(highProductBaseInfoBean.getFundType());
            baseInfo.setFundShareClass(highProductBaseInfoBean.getShareClass());
            baseInfo.setProductChannel(highProductBaseInfoBean.getProductChannel());
            baseInfo.setBalanceVol(MoneyUtil.formatMoney(book.getBalanceVol(), 2));
            // 银行卡信息
            QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
            if (result != null) {
                baseInfo.setBankCode(result.getBankCode());
                baseInfo.setBankName(result.getBankRegionName());
                baseInfo.setBankAcctNo(result.getBankAcct());
            }
            baseInfoList.add(baseInfo);
        }
        for (AcctBalanceDetailBaseInfo baseInfo : baseInfoList) {
            baseInfo.setTotalVol(totalVol);
            baseInfo.setTotalUnConfirmVol(totalUnConfirmVol);
            baseInfo.setTotalAvailVol(totalAvailVol);
            baseInfo.setTotalRedeemAllSurplusVol(totalRedeemAllSurplusVol);
        }
    }

    /**
     * getSubmitDt:(获取上报日期)
     *
     * @param highProductBaseInfoBean 产品基本信息
     * @param disCode                 分销机构代码
     * @param taTradeDt               当前日期所属交易日
     * @param appDate                 申请日期
     * @return
     * <AUTHOR>
     * @date 2018年6月15日 下午2:19:11
     */
    private String getSubmitDt(HighProductBaseInfoBean highProductBaseInfoBean, String disCode, String taTradeDt, Date appDate) {
        log.info("QueryAcctBalanceDtlFacadeService|getTaTradeDt|highProductBaseInfoBean:{}, disCode:{},  taTradeDt:{}, appDate:{}", JSON.toJSONString(highProductBaseInfoBean), disCode, taTradeDt, appDate);
        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())
                || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {

            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(highProductBaseInfoBean.getFundCode(), "1", highProductBaseInfoBean.getShareClass(), disCode, appDate);
            if (productAppointmentInfoBean != null) {
                if (taTradeDt.compareTo(productAppointmentInfoBean.getOpenStartDt()) < 0) {
                    return productAppointmentInfoBean.getOpenStartDt();
                }
            }
        }

        return taTradeDt;

    }

    @Override
    public Map<String, OwnershipOrderDto> getOwnershipOrderInfoMap(QueryAcctBalanceBaseParam param) {
        log.info("getOwnershipOrderInfoMap-param={}", JSON.toJSONString(param));
        if (CollectionUtils.isEmpty(param.getFundCodeList())) {
            return new HashMap<>();
        }
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setTxAcctNo(param.getTxAcctNo());
        queryAcctBalanceBaseParam.setHbOneNo(param.getHbOneNo());
        queryAcctBalanceBaseParam.setDisCodeList(param.getDisCodeList());
        queryAcctBalanceBaseParam.setFundCodeList(param.getFundCodeList());
        List<OwnershipOrderInfo> ownershipOrderInfos = queryAcctOwnershipOrderInfo(queryAcctBalanceBaseParam);
        // 按照商品维度,计算成本
        Map<String, List<OwnershipOrderInfo>> productMap = ownershipOrderInfos.stream().collect(Collectors.groupingBy(OwnershipOrderInfo::getFundCode));
        HashMap<String, OwnershipOrderDto> netBuyAmtMap = new HashMap<>(productMap.size());
        productMap.forEach((fundCode, orders) -> {
            ProductOrderInfo productOrderInfo = new ProductOrderInfo();
            productOrderInfo.setProductCode(fundCode);
            productOrderInfo.setOrderInfoList(orders);
            netBuyAmtMap.put(fundCode, new OwnershipOrderDto(productOrderInfo.getNetBuyAmount(), productOrderInfo.getOwnershipTransferIdentity()));
        });
        return netBuyAmtMap;
    }

    @Override
    public String getFirstBuyFlag(String fundCode, String txAcctNo, String disCode) {
        log.info("getFirstBuyFlag-获取是否首次购买-fundCode={},txAcctNo={},disCode={}", fundCode, txAcctNo, disCode);
        // 1.查询产品基础信息
        HighProductInfoBean highProductBaseInfo = queryHighProductOuterService.getHighProductInfo(fundCode);
        if (DisCodeEnum.HZ.getCode().equals(disCode)) {
            if (!YesOrNoEnum.YES.getCode().equals(highProductBaseInfo.getPeDivideCallFlag())) {
                boolean isSuppleSubs = suppleSubsService.isSuppleSubs(txAcctNo, fundCode);
                log.info("getFirstBuyFlag-好臻非分次call产品,isSuppleSubs={}", isSuppleSubs);
                return isSuppleSubs ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode();
            } else {
                // 好臻的,分次call,如果没有确认持仓就是首单
                QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
                param.setTxAcctNo(txAcctNo);
                param.setFundCodeList(Collections.singletonList(fundCode));
                param.setDisCodeList(Collections.singletonList(disCode));
                List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = queryConfirmBalanceBaseInfo(param);
                log.info("getFirstBuyFlag-好臻分次call产品,confirmBalanceBaseInfoList={}", confirmBalanceBaseInfoList);
                return CollectionUtils.isEmpty(confirmBalanceBaseInfoList) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
            }
        } else {
            boolean isSuppleSubs = suppleSubsService.isSuppleSubs(txAcctNo, fundCode);
            log.info("getFirstBuyFlag-非好臻产品,isSuppleSubs={}", isSuppleSubs);
            return isSuppleSubs ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode();
        }
    }

    /**
     * 参数校验与赋值
     */
    private void checkAndBuildParam(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam) {
        if (StringUtils.isBlank(queryAcctBalanceBaseParam.getTxAcctNo()) && StringUtils.isBlank(queryAcctBalanceBaseParam.getHbOneNo())) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "一账通/交易账号不能同时为空");
        }
        if (CollectionUtils.isEmpty(queryAcctBalanceBaseParam.getFundCodeList())) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "产品编码不能为空");
        }

        if (StringUtils.isBlank(queryAcctBalanceBaseParam.getTxAcctNo())) {
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(queryAcctBalanceBaseParam.getHbOneNo());
            queryAcctBalanceBaseParam.setTxAcctNo(txAcctNo);
        }
        if (StringUtils.isBlank(queryAcctBalanceBaseParam.getHbOneNo())) {
            String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(queryAcctBalanceBaseParam.getTxAcctNo());
            queryAcctBalanceBaseParam.setHbOneNo(hbOneNo);
        }
    }

    /**
     * 获取转义的业务编码
     *
     * @param dealNo                 订单号
     * @param fundCode               产品编码
     * @param currBusinessCode       当前订单的业务编码
     * @param isFirstOrder           该产品+业务类型+该用户是否是首次
     * @param businessCodeConfigList 配置信息
     * @return 转义后的业务编码
     */
    public String getBusinessTransferCode(String dealNo, String fundCode, String currBusinessCode, boolean isFirstOrder, List<BusinessCodeTransferConfigInfoBean> businessCodeConfigList) {
        // 1.查询所有配置
        if (CollectionUtils.isEmpty(businessCodeConfigList)) {
            log.info("getBusinessTransferCode-没有任何业务转义配置,dealNo={}", dealNo);
            return currBusinessCode;
        }
        // 2.遍历匹配配置
        for (BusinessCodeTransferConfigInfoBean businessConfBean : businessCodeConfigList) {
            // 2.1.首先按照交易类型+产品去匹配,匹配不上就不需要走后面逻辑
            if (!currBusinessCode.equals(businessConfBean.getCurrMBusinessCode()) || !fundCode.equals(businessConfBean.getFundCode())) {
                continue;
            }
            // 2.2.按照订单号匹配
            if (StringUtils.isNotBlank(businessConfBean.getDealNo())) {
                if (businessConfBean.getDealNo().equals(dealNo)) {
                    return businessConfBean.getTransferMBusinessCode();
                }
            }

            // 2.3.如果没有设置订单号,如果每次都生效,直接返回每次都生效的配置转义
            if (EffectiveType.EVERY_ORDER.getType() == businessConfBean.getEffectiveType()) {
                return businessConfBean.getTransferMBusinessCode();
            }
            // 2.4.仅首次生效
            if (EffectiveType.FIRST_ORDER.getType() == businessConfBean.getEffectiveType() && isFirstOrder) {
                return businessConfBean.getTransferMBusinessCode();
            }
        }
        // 没有匹配上,就不需要转义
        return currBusinessCode;
    }


}
