<?xml version='1.0' encoding='UTF-8'?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements. See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership. The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.shared</groupId>
    <artifactId>maven-shared-components</artifactId>
    <version>21</version>
    <relativePath>../maven-shared-components/pom.xml</relativePath>
  </parent>

  <artifactId>maven-shared-utils</artifactId>
  <version>3.0.0</version>

  <name>Apache Maven Shared Utils</name>
  <description>Shared utils without any further dependencies</description>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/shared/tags/maven-shared-utils-3.0.0</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/shared/tags/maven-shared-utils-3.0.0
    </developerConnection>
    <url>http://svn.apache.org/viewvc/maven/shared/tags/maven-shared-utils-3.0.0</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/MSHARED/component/12326452</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <checkstyle.violation.ignore>RedundantThrows,NewlineAtEndOfFile,ParameterNumber,MethodLength,FileLength,ModifierOrder</checkstyle.violation.ignore>

    <mavenVersion>3.0</mavenVersion>
    <maven.compiler.target>1.6</maven.compiler.target>
    <maven.compiler.source>1.6</maven.compiler.source>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.4</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>2.0.1</version>
    </dependency>
    <!--
      ! Maven Core is used in context with Maven cause
      ! it is needed for Toolchain access.
    -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>2.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.5.5</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <!--
          ! After a new parent (version 22) has been released this version can be removed.
        -->
        <version>2.15</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>findbugs-exclude.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes combine.children="append">
            <exclude>src/test/resources/directorywalker/**/*</exclude>
            <exclude>src/test/resources/symlinks/**/*</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <!--
              ! After a new parent (version 22) has been released this version can be removed.
            -->
            <version>2.15</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
