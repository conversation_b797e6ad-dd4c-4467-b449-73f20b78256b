package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.interlayer.product.model.fund.ProductAppointmentInfoModel;
import com.howbuy.interlayer.product.service.ProductAppointmentInfoService;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.common.Constants;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.CustomerRedeemAppointInfo;
import com.howbuy.tms.high.orders.service.service.redeemLogicService.QueryCustomerRedeemAppointInfoLogicService;
import com.howbuy.tms.high.orders.service.service.subcustbooks.SubCustBooksService;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:查询用户赎回日历信息
 * @Author: yun.lu
 * Date: 2024/10/9 17:01
 */
@Data
public class QueryCustomerRedeemAppointInfoTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryCustomerRedeemAppointInfoTask.class);
    private QueryHighProductOuterService queryHighProductOuterService;
    private String workDt;
    private List<String> fundCodeList;
    private List<CustomerRedeemAppointInfo> customerRedeemAppointInfoList;
    private SubCustBooksService subCustBooksService;
    private ProductAppointmentInfoService productAppointmentInfoService;
    private QueryCustomerRedeemAppointInfoLogicService queryCustomerRedeemAppointInfoLogicService;

    @Override
    protected void callTask() {
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return;
        }
        // 1.查询产品基础信息
        List<HighProductBaseInfoBean> highProductBaseInfoList = queryHighProductOuterService.getHighProductBaseInfoList(fundCodeList);
        // 2.根据产品查询产品份额明细
        List<SubCustBooksPo> allSubCustomerBooksPoList = subCustBooksService.queryByFundCode(fundCodeList);
        if (CollectionUtils.isEmpty(allSubCustomerBooksPoList)) {
            log.info("根据产品查询不到份额明细,fundCodeList={}", fundCodeList);
            return;
        }
        // 3.根据份额锁定期查询预约日历
        List<String> allFundCodeList = allSubCustomerBooksPoList.stream().map(SubCustBooksPo::getFundCode).distinct().collect(Collectors.toList());
        Map<String, List<SubCustBooksPo>> subCustBooksMap = allSubCustomerBooksPoList.stream().collect(Collectors.groupingBy(SubCustBooksPo::getFundCode));
        Map<String, HighProductBaseInfoBean> fundBaseMap = highProductBaseInfoList.stream().collect(Collectors.toMap(HighProductBaseInfoBean::getFundCode, x -> x));
        for (String fundCode : allFundCodeList) {
            // 4.查询产品是否支持份额锁定
            HighProductBaseInfoBean highProductBaseInfoBean = fundBaseMap.get(fundCode);
            if (highProductBaseInfoBean == null) {
                log.info("根据产品编码查不到产品基础信息,fundCode={}", fundCode);
                continue;
            }
            if (YesOrNoEnum.NO.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                log.info("查询产品是不支持份额锁定,fundCode={}", fundCode);
                continue;
            }
            List<SubCustBooksPo> subCustBooksPoList = subCustBooksMap.get(fundCode);

            List<String> openRedeemDtList = subCustBooksPoList.stream().map(x -> {
                if ("00010101".equals(x.getOpenRedeDt())) {
                    return "20991231";
                }
                return x.getOpenRedeDt();
            }).distinct().collect(Collectors.toList());
            Map<String, List<SubCustBooksPo>> subListMap = subCustBooksPoList.stream().collect(Collectors.groupingBy(x -> x.getTxAcctNo() + Constants.UNDERLINE + x.getFundCode()));
            Map<String, ProductAppointmentInfoModel> productAppointmentInfoModelMap = productAppointmentInfoService.queryEffectOrWillEffectAppointByOpenDt(fundCode, "1", openRedeemDtList);
            // 5.份额明细处理
            for (SubCustBooksPo subCustBooksPo : subCustBooksPoList) {
                List<SubCustBooksPo> subList = subListMap.get(subCustBooksPo.getTxAcctNo() + Constants.UNDERLINE + subCustBooksPo.getFundCode());
                BigDecimal totalVol = BigDecimal.ZERO;
                for (SubCustBooksPo custBooksPo : subList) {
                    totalVol = totalVol.add(custBooksPo.getBalanceVol());
                }
                ProductAppointmentInfoModel productAppointmentInfoModel = productAppointmentInfoModelMap.get(subCustBooksPo.getOpenRedeDt());
                CustomerRedeemAppointInfo customerRedeemAppointInfo = queryCustomerRedeemAppointInfoLogicService.getCustomerRedeemAppointInfo(workDt, fundCode, totalVol, highProductBaseInfoBean, productAppointmentInfoModel, subCustBooksPo);
                customerRedeemAppointInfoList.add(customerRedeemAppointInfo);
            }

        }
    }


}
