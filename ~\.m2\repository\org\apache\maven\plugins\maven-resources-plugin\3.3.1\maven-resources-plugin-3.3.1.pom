<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>39</version>
  </parent>

  <artifactId>maven-resources-plugin</artifactId>
  <version>3.3.1</version>
  <packaging>maven-plugin</packaging>

  <name>Apache Maven Resources Plugin</name>
  <description>The Resources Plugin handles the copying of project resources to the output
    directory. There are two different kinds of resources: main resources and test resources. The
    difference is that the main resources are the resources associated with the main
    source code while the test resources are associated with the test source code.
    Thus, this allows the separation of resources for the main source code and its
    unit tests.</description>
  <inceptionYear>2001</inceptionYear>

  <contributors>
    <contributor>
      <name>Graham Leggett</name>
    </contributor>
  </contributors>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-resources-plugin.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-resources-plugin.git</developerConnection>
    <tag>maven-resources-plugin-3.3.1</tag>
    <url>https://github.com/apache/maven-resources-plugin/tree/${project.scm.tag}</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/MRESOURCES</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-builds.apache.org/job/Maven/job/maven-box/job/maven-resources-plugin/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <mavenFilteringVersion>3.3.1</mavenFilteringVersion>
    <mavenVersion>3.2.5</mavenVersion>
    <javaVersion>8</javaVersion>
    <project.build.outputTimestamp>2023-03-21T12:00:59Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-settings</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-interpolation</artifactId>
      <version>1.26</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.sisu</groupId>
      <artifactId>org.eclipse.sisu.plexus</artifactId>
      <version>0.3.5</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.5.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-filtering</artifactId>
      <version>${mavenFilteringVersion}</version>
    </dependency>
    <!-- Upgrade of transitive commons-io 2.4 of maven-shared-utils of maven-filtering. -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-compat</artifactId>
      <version>${mavenVersion}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>3.3.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-api</artifactId>
      <version>1.6.3</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes combine.children="append">
            <!-- ITs and IT resources -->
            <exclude>src/it/**</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <configuration>
                <debug>true</debug>
                <postBuildHookScript>verify</postBuildHookScript>
                <preBuildHookScript>setup</preBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <goals>
                  <goal>clean</goal>
                  <goal>process-test-resources</goal>
                </goals>
                <settingsFile>src/it/settings.xml</settingsFile>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <properties>
                  <execProps>fromExecProps</execProps>
                </properties>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <groupId>org.eclipse.sisu</groupId>
            <artifactId>sisu-maven-plugin</artifactId>
          </plugin>
          <!--
           ! The following is used within the src/it/user-filters test.
          -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
