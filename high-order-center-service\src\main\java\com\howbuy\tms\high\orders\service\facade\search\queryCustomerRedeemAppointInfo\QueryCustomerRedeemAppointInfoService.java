package com.howbuy.tms.high.orders.service.facade.search.queryCustomerRedeemAppointInfo;

import com.howbuy.interlayer.product.model.WorkDayModel;
import com.howbuy.interlayer.product.service.ProductAppointmentInfoService;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.utils.PartListUtil;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.CustomerRedeemAppointInfo;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.QueryCustomerRedeemAppointInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.QueryCustomerRedeemAppointInfoRequest;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.QueryCustomerRedeemAppointInfoResponse;
import com.howbuy.tms.high.orders.service.business.task.HowBuyRunTaskUil;
import com.howbuy.tms.high.orders.service.business.task.QueryCustomerRedeemAppointInfoTask;
import com.howbuy.tms.high.orders.service.service.redeemLogicService.QueryCustomerRedeemAppointInfoLogicService;
import com.howbuy.tms.high.orders.service.service.subcustbooks.SubCustBooksService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:查询用户赎回日历信息
 * @Author: yun.lu
 * Date: 2024/10/9 16:52
 */
@DubboService
@Service("queryCustomerRedeemAppointInfoFacade")
@Slf4j
public class QueryCustomerRedeemAppointInfoService implements QueryCustomerRedeemAppointInfoFacade {
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private SubCustBooksService subCustBooksService;
    @Autowired
    private ProductAppointmentInfoService productAppointmentInfoService;
    @Autowired
    private QueryCustomerRedeemAppointInfoLogicService queryCustomerRedeemAppointInfoLogicService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    @Autowired
    private TradeDayService tradeDayService;

    @Override
    public QueryCustomerRedeemAppointInfoResponse execute(QueryCustomerRedeemAppointInfoRequest request) {
        log.info("QueryCustomerRedeemAppointInfoService-查询用户赎回日历信息,fundCodeList={}", request.getFundCodeList());
        QueryCustomerRedeemAppointInfoResponse response = new QueryCustomerRedeemAppointInfoResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        if (CollectionUtils.isEmpty(request.getFundCodeList())) {
            log.info("QueryCustomerRedeemAppointInfoService-基金代码为空");
            return response;
        }
        WorkDayModel workDayModel = tradeDayService.getWorkDayModel(new Date());
        // 1.按照基金代码分组
        List<List<String>> partitionFundList = PartListUtil.partition(request.getFundCodeList(), 10);
        // 2.多线程处理
        List<CustomerRedeemAppointInfo> customerRedeemAppointInfoList = new ArrayList<>();
        List<QueryCustomerRedeemAppointInfoTask> taskList = new ArrayList<>();
        for (List<String> subFundCodeList : partitionFundList) {
            QueryCustomerRedeemAppointInfoTask queryCustomerRedeemAppointInfoTask = new QueryCustomerRedeemAppointInfoTask();
            queryCustomerRedeemAppointInfoTask.setCustomerRedeemAppointInfoList(customerRedeemAppointInfoList);
            queryCustomerRedeemAppointInfoTask.setSubCustBooksService(subCustBooksService);
            queryCustomerRedeemAppointInfoTask.setQueryHighProductOuterService(queryHighProductOuterService);
            queryCustomerRedeemAppointInfoTask.setProductAppointmentInfoService(productAppointmentInfoService);
            queryCustomerRedeemAppointInfoTask.setWorkDt(workDayModel.getWorkday());
            queryCustomerRedeemAppointInfoTask.setFundCodeList(subFundCodeList);
            queryCustomerRedeemAppointInfoTask.setQueryCustomerRedeemAppointInfoLogicService(queryCustomerRedeemAppointInfoLogicService);
            taskList.add(queryCustomerRedeemAppointInfoTask);
        }
        howBuyRunTaskUil.runTask(taskList);
        response.setCustomerRedeemAppointInfoList(customerRedeemAppointInfoList);
        return response;
    }
}
