/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.constants;

/**
 * @description: 持仓查询相关常量
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
public class BalanceConstants {

    /**
     * 持仓状态
     */
    public static class BalanceStatus {
        /** 不持仓 */
        public static final String NO_BALANCE = "0";
        /** 持仓 */
        public static final String HAS_BALANCE = "1";
        /** 全部 */
        public static final String ALL = "2";
    }

    /**
     * 规模类型
     */
    public static class ScaleType {
        /** 代销 */
        public static final String CONSIGNMENT = "0";
        /** 直销 */
        public static final String DIRECT = "1";
    }

    /**
     * 调用类型
     */
    public static class CallType {
        /** 新资产中心 */
        public static final String NEW_ASSET_CENTER = "1";
        /** 老资产中心 */
        public static final String OLD_ASSET_CENTER = "2";
    }

    /**
     * 净值分红标识
     */
    public static class NavDivFlag {
        /** 否 */
        public static final String NO = "0";
        /** 是 */
        public static final String YES = "1";
    }

    /**
     * 收益计算状态
     */
    public static class IncomeCalStat {
        /** 计算中 */
        public static final String PROCESSING = "0";
        /** 计算完成 */
        public static final String FINISHED = "1";
    }

    /**
     * 协议类型
     */
    public static class ProtocolType {
        /** 高端产品协议 */
        public static final String HIGH_END = "4";
    }

    /**
     * 数值常量
     */
    public static class Numbers {
        /** 金额精度 */
        public static final int MONEY_SCALE = 2;
        /** 直销持仓判断阈值 */
        public static final String DIRECT_BALANCE_THRESHOLD = "1";
    }

    /**
     * 产品特殊标识
     */
    public static class ProductFlags {
        /** 千禧标识 */
        public static final String QIAN_XI_FLAG = "1";
        /** 危机标识 */
        public static final String CRISIS_FLAG = "1";
        /** 异常标识 */
        public static final String ABNORMAL_FLAG = "1";
        /** 分次call标识 */
        public static final String FRACTIONATE_CALL_FLAG = "1";
        /** 分期成立标识 */
        public static final String STAGE_ESTABLISH_FLAG = "1";
    }
}
