<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.dtms</groupId>
    <artifactId>dtms-common</artifactId>
    <version>1.9.4.0-RELEASE</version>
    <packaging>pom</packaging>
    <name>dtms-common</name>

    <properties>
        
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <dubbo.version>2.7.15</dubbo.version>
        <druid.version>1.2.8</druid.version>
        <log4j.version>2.15.0</log4j.version>
        <mybatis.version>2.2.2</mybatis.version>
        <zkclient.version>0.4</zkclient.version>
        <fastjson.version>1.2.80</fastjson.version>
        <disruptor.version>3.4.2</disruptor.version>
        <zookeeper.version>3.4.13</zookeeper.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <log4j.version>2.15.0</log4j.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <log4j-over-slf4j.version>1.7.29</log4j-over-slf4j.version>
        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        <redis.clients.version>2.9.3</redis.clients.version>
        
        <spring-support-alibaba.version>1.0.11</spring-support-alibaba.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-rocket.version>2.2.1-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-message-amq.version>2.2.1-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-service.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.util.version>1.0.0-SNAPSHOT</com.howbuy.util.version>
        <com.howbuy.dtms-common.version>1.0.0-RELEASE</com.howbuy.dtms-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-support-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>${com.howbuy.util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-amq</artifactId>
                <version>${com.howbuy.howbuy-message-amq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.9.3</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://nexus.howbuy.pa/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://nexus.howbuy.pa/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
    </project>