/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.business.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.paycommon.model.enums.AuthStateEnum;
import com.howbuy.paycommon.model.enums.IdTypeEnum;
import com.howbuy.paycommon.model.enums.TermTypeEnum;
import com.howbuy.tms.cache.service.hithauthcache.HighAuthCacheService;
import com.howbuy.tms.common.enums.busi.AuthMsgTypeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.outerservice.acccenter.convenientvrifyformobile.ConvenientVrifyForMobileContext;
import com.howbuy.tms.common.outerservice.acccenter.convenientvrifyformobile.ConvenientVrifyForMobileOuterService;
import com.howbuy.tms.common.outerservice.acccenter.convenientvrifyformobile.ConvenientVrifyForMobileResult;
import com.howbuy.tms.common.outerservice.auth.encryptsingle.EncryptSingleOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.SendMessageOuterService;
import com.howbuy.tms.common.outerservice.es.authentication.GetMobileVerificationCodeContext;
import com.howbuy.tms.common.outerservice.es.authentication.GetMobileVerificationCodeOuterService;
import com.howbuy.tms.common.outerservice.es.authentication.GetMobileVerificationCodeResult;
import com.howbuy.tms.common.outerservice.payonline.quickcardauth.QuickCardAuthContext;
import com.howbuy.tms.common.outerservice.payonline.quickcardauth.QuickCardAuthOuterOuterService;
import com.howbuy.tms.common.outerservice.payonline.quickcardauth.QuickCardAuthResult;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgRequest;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.ConvenientVrifyForMobileBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.MessageCenterConterxtBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.QuickCardAuthContextBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:(发送鉴权短信任务)
 * @reason:
 * <AUTHOR>
 * @date 2018年8月31日 下午9:25:49
 * @since JDK 1.6
 */
public class SendAuthMsgTask extends Thread {
    private static final Logger logger = LoggerFactory.getLogger(SendAuthMsgTask.class);

    //好买通道
    public static final String SEND_MSG_CHANNEL_HOWBUY = "0";

    //支付通道
    public static final String SEND_MSG_CHANNEL_PAY = "1";

    //e签宝通道
    public static final String SEND_MSG_CHANNEL_ES = "2";

    private HighAuthCacheService highAuthCacheService;
    private QuickCardAuthOuterOuterService quickCardAuthOuterOuterService;
    private ConvenientVrifyForMobileOuterService convenientVrifyForMobileOuterService;
    private SendMessageOuterService sendMessageOuterService;
    private GetMobileVerificationCodeOuterService getMobileVerificationCodeOuterService;
    private SendAuthMsgRequest sendAuthMsgRequest;
    private EncryptSingleOuterService encryptSingleOuterService;


    public SendAuthMsgTask(HighAuthCacheService highAuthCacheService, QuickCardAuthOuterOuterService quickCardAuthOuterOuterService,
                           ConvenientVrifyForMobileOuterService convenientVrifyForMobileOuterService,
                           SendMessageOuterService sendMessageOuterService, GetMobileVerificationCodeOuterService getMobileVerificationCodeOuterService,
                           SendAuthMsgRequest sendAuthMsgRequest, EncryptSingleOuterService encryptSingleOuterService) {

        this.highAuthCacheService = highAuthCacheService;
        this.quickCardAuthOuterOuterService = quickCardAuthOuterOuterService;
        this.convenientVrifyForMobileOuterService = convenientVrifyForMobileOuterService;
        this.sendMessageOuterService = sendMessageOuterService;
        this.getMobileVerificationCodeOuterService = getMobileVerificationCodeOuterService;
        this.sendAuthMsgRequest = sendAuthMsgRequest;
        this.encryptSingleOuterService = encryptSingleOuterService;
    }

    @Override
    public void run() {
        logger.info("GenerateAuthCodeTask|start");
        try {
            // 获取验证码
            getAuthenticateCode(sendAuthMsgRequest);
        } catch (Exception e) {
            logger.error("GenerateAuthCodeTask|error:{}", e.getMessage(), e);
        } finally {
            logger.info("GenerateAuthCodeTask|end");
        }

    }

    /**
     *
     * getAuthenticateCode:(获取验证码)
     * @param sendAuthMsgRequest
     * <AUTHOR>
     * @date 2018年8月30日 下午5:50:25
     */
    private void getAuthenticateCode(SendAuthMsgRequest sendAuthMsgRequest) {
        logger.info("GenerateAuthCodeTask|getAuthenticateCode|params|sendAuthMsgRequest:{}", JSON.toJSONString(sendAuthMsgRequest));
      // 短信鉴权结果
        Map<String, String> authRst = getMap(sendAuthMsgRequest);
        // 支付发送鉴权短信成功状态
        boolean paySendMsgSuccStaus = true;
        String authenticateSeriousNo = "";
        try {
            if (AuthMsgTypeEnum.E_AUTH_MSG_TYPE.getCode().equals(sendAuthMsgRequest.getAuthMsgType())) {
                // e签宝鉴权
                GetMobileVerificationCodeContext context = new GetMobileVerificationCodeContext();
                context.setIdNo(sendAuthMsgRequest.getIdNo());
                context.setIdType(sendAuthMsgRequest.getIdType());
                context.setMobile(sendAuthMsgRequest.getMobile());
                context.setName(sendAuthMsgRequest.getCustName());
                GetMobileVerificationCodeResult result = getMobileVerificationCodeOuterService.getMobileVerificationCode(context);
                if (result != null && "0".equals(result.getRetCode())) {
                    // 鉴权类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝
                    authRst.put("authMsgType", "4");
                    // 鉴权通道
                    authRst.put("sendMsgChannel", SEND_MSG_CHANNEL_ES);
                } else {
                    // e签宝发短信失败，调用好买短信通道
                    paySendMsgSuccStaus = false;
                }
            } else if (AuthMsgTypeEnum.HOWBUY_MSG_TYPE.getCode().equals(sendAuthMsgRequest.getAuthMsgType())) {
                // 调用好买短信
                paySendMsgSuccStaus = false;
            } else {
                if (AuthMsgTypeEnum.QUICK_AUTH_MSG_TYPE.getCode().equals(sendAuthMsgRequest.getAuthMsgType())) {
                    QuickCardAuthContextBean quickCardAuthContextBean = sendAuthMsgRequest.getQuickCardAuthContextBean();
                    QuickCardAuthContext quickCardAuthContext = new QuickCardAuthContext();
                    quickCardAuthContext.setTxAcctNo(quickCardAuthContextBean.getTxAcctNo());
                    quickCardAuthContext.setMobile(quickCardAuthContextBean.getMobile());
                    quickCardAuthContext.setCustName(quickCardAuthContextBean.getCustName());
                    quickCardAuthContext.setIdNo(quickCardAuthContextBean.getIdNo());
                    quickCardAuthContext.setIdType(IdTypeEnum.getValue(quickCardAuthContextBean.getIdType()));
                    quickCardAuthContext.setBankAcct(quickCardAuthContextBean.getBankAcct());
                    quickCardAuthContext.setBankCode(quickCardAuthContextBean.getBankCode());

                    quickCardAuthContext.setTermType(TermTypeEnum.WAP);
                    if (!StringUtil.isEmpty(sendAuthMsgRequest.getTxChannel())) {
                        if (TxChannelEnum.APP.getCode().equals(sendAuthMsgRequest.getTxChannel())) {
                            quickCardAuthContext.setTermType(TermTypeEnum.APP);
                        }
                    }
                    // 快捷鉴权
                    QuickCardAuthResult quickCardAuthResult = quickCardAuthOuterOuterService.quickCardAuth(quickCardAuthContext);
                    if (null != quickCardAuthResult) {
                        if (AuthStateEnum.AUTH_FAIL.equals(quickCardAuthResult.getAuthState())) {
                            logger.info("getAuthenticateCode|quickCardAuth faild");
                            paySendMsgSuccStaus = false;
                        } else {
                            authenticateSeriousNo = quickCardAuthResult.getApplyDealNo();
                        }

                    } else {
                        logger.info("getAuthenticateCode|quickCardAuth faild");
                        paySendMsgSuccStaus = false;
                    }
                } else {
                    ConvenientVrifyForMobileBean convenientVrifyForMobileBean = sendAuthMsgRequest.getConvenientVrifyForMobileBean();
                    ConvenientVrifyForMobileContext convenientVrifyForMobileContext = new ConvenientVrifyForMobileContext();
                    convenientVrifyForMobileContext.setTxAcctNo(convenientVrifyForMobileBean.getTxAcctNo());
                    convenientVrifyForMobileContext.setBankAcct(convenientVrifyForMobileBean.getBankAcct());
                    convenientVrifyForMobileContext.setVerifyFlag(convenientVrifyForMobileBean.getVerifyFlag());
                    convenientVrifyForMobileContext.setOldMobileNo(convenientVrifyForMobileBean.getOldMobileNo());
                    convenientVrifyForMobileContext.setMobileNo(convenientVrifyForMobileBean.getMobileNo());
                    // 修改手机号
                    ConvenientVrifyForMobileResult convenientVrifyForMobileResult = convenientVrifyForMobileOuterService.convenientVrifyForMobile(convenientVrifyForMobileContext);

                    if (null != convenientVrifyForMobileResult) {
                        if ("01".equals(convenientVrifyForMobileResult.getVrfyStat())) {
                            // 申请状态 00-成功，01-失败，02-鉴权中
                            paySendMsgSuccStaus = false;
                        } else {
                            authenticateSeriousNo = convenientVrifyForMobileResult.getContractNo();
                        }

                    } else {
                        paySendMsgSuccStaus = false;
                        logger.info("getAuthenticateCode|convenientVrifyForMobile failed");
                    }
                }
            }

        } catch (Exception e) {
            logger.error("getAuthenticateCode|error :{}", e.getMessage(), e);
        } finally {
            endProcess(sendAuthMsgRequest, authRst, paySendMsgSuccStaus, authenticateSeriousNo);
        }

    }

    private void endProcess(SendAuthMsgRequest sendAuthMsgRequest, Map<String, String> authRst, boolean paySendMsgSuccStaus, String authenticateSeriousNo) {
        boolean setCacheFlag = true;
        if (!paySendMsgSuccStaus) {
            // 支付发送短信失败，手机号是好买预留,调用好买消息中心发送
            MessageCenterConterxtBean messageCenterConterxtBean = sendAuthMsgRequest.getMessageCenterConterxtBean();
            if (sendAuthMsgRequest.isMobileExit()) {

                String mobile = encryptSingleOuterService.encryptSingle(messageCenterConterxtBean.getMobile());
                authenticateSeriousNo = sendMessageOuterService.sendMsg(messageCenterConterxtBean.getBusinessId(),
                        messageCenterConterxtBean.getCustNo(), messageCenterConterxtBean.getCustType(),
                        mobile, messageCenterConterxtBean.getRandom());
                authRst.put("sendMsgChannel", SEND_MSG_CHANNEL_HOWBUY);// 好买消息中心
                authRst.put("authCode", messageCenterConterxtBean.getRandom());
                authRst.put("realAuthMsgType", "3");// 实际发送鉴权信息类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝
            } else {
                logger.error("手机号{} 不存在", messageCenterConterxtBean.getMobile());
                setCacheFlag = false;
            }
        }
        if (setCacheFlag) {
            authRst.put("authenticateSeriousNo", authenticateSeriousNo);
            // 短信鉴权流水号保存在缓冲，校验短信验证码使用
            highAuthCacheService.put(sendAuthMsgRequest.getSendMsgReqId(), JSON.toJSONString(authRst));
        }
    }

    private static Map<String, String> getMap(SendAuthMsgRequest sendAuthMsgRequest) {
        Map<String, String> authRst = new HashMap<String, String>();
        authRst.put("sendMsgChannel", SEND_MSG_CHANNEL_PAY);// 鉴权通道
        authRst.put("authenticateSeriousNo", "");// 鉴权流水
        authRst.put("authCode", "");// 好买短信验证码
        authRst.put("authMsgType", sendAuthMsgRequest.getAuthMsgType());// 鉴权类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝
        authRst.put("realAuthMsgType", sendAuthMsgRequest.getAuthMsgType());// 实际发送鉴权信息类型
        return authRst;
    }

}

