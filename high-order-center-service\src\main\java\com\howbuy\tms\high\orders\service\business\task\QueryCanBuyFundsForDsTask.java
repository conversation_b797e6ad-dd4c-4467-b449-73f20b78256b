/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.business.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.ProductBuyUserTypeEnum;
import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.BranchCodeEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.enums.database.TxOpenFlagEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductTxOpenCfgBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.ConvertCodeUtils;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsRequest;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.bean.CanBuyFunds;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2022/5/6 9:21
 * @since JDK 1.8
 */
public class QueryCanBuyFundsForDsTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryCanBuyFundsForDsTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;
    private List<HighProductInfoModel> highProductInfoModelList;
    private QueryCanBuyFundsForDsRequest request;
    private String taTradeDt;
    private CountDownLatch latch;
    private String ordersKey;
    private List<CanBuyFunds.FundInfo> fundInfos;

    public QueryCanBuyFundsForDsTask(QueryHighProductOuterService queryHighProductOuterService,
                                     List<HighProductInfoModel> highProductInfoModelList,
                                     QueryCanBuyFundsForDsRequest request, String taTradeDt, CountDownLatch latch,
                                     String ordersKey, List<CanBuyFunds.FundInfo> fundInfos) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.highProductInfoModelList = highProductInfoModelList;
        this.request = request;
        this.taTradeDt = taTradeDt;
        this.latch = latch;
        this.ordersKey = ordersKey;
        this.fundInfos = fundInfos;
    }


    @Override
    public RuntimeException call() throws Exception {
        try {
            for (HighProductInfoModel highProductInfoModel : highProductInfoModelList) {
                try {
                    String productCode = highProductInfoModel.getFundCode();
                    logger.info("highProductInfoModel:{}", JSON.toJSONString(highProductInfoModel));

                    // TA状态校验
                    if (!YesOrNoEnum.NO.getCode().equals(highProductInfoModel.getTaStat())) {
                        logger.info("productCode:{},ta状态不正常",highProductInfoModel.getTaStat());
                        continue;
                    }

                    // 代销关系校验
                    if (!validIsSupCounterOrWeb(highProductInfoModel, request.getTxChannel())) {
                        logger.info("productCode:{},购买渠道不支持",productCode);
                        continue;
                    }

                    // 产品可购买类型校验
                    if (!validIsSupBuyInvstType(highProductInfoModel, ProductBuyUserTypeEnum.PERSONAL.getCode())) {
                        logger.info("productCode:{},购买类型校验不支持",productCode);
                        continue;
                    }

                    // 信托产品校验
                    if(YesOrNoEnum.YES.getCode().equals(highProductInfoModel.getIsTrust())){
                        logger.info("productCode:{},信托产品不支持",productCode);
                        continue;
                    }

                    // 分销渠道校验
                    if(DisCodeEnum.LCT.getCode().equals(highProductInfoModel.getDisCode())){
                        logger.info("productCode:{},分销渠道为理财通",productCode);
                        continue;
                    }

                    // 校验预约期和状态
                    if(!validateProductTradeStatus(highProductInfoModel, request, taTradeDt)){
                        logger.info("productCode:{},校验预约期和状态失败",productCode);
                        continue;
                    }
                    CanBuyFunds.FundInfo fundInfo = new CanBuyFunds.FundInfo();
                    fundInfo.setFundCode(highProductInfoModel.getFundCode());
                    fundInfo.setFundName(highProductInfoModel.getFundName());
                    fundInfos.add(fundInfo);

                } catch (RuntimeException e) {

                    logger.error("QueryCanBuyFundsForDsTask|error, fundCode:{}, msg:{}", highProductInfoModel.getFundCode(), e.getMessage(), e);
                }
            }
        } catch (RuntimeException e) {
            logger.error("QueryCanBuyFundsForDsTask|error, msg:{}", e.getMessage(), e);
        } finally {
            latch.countDown();
        }
        return null;
    }

    /**
     * @description:(校验代销关系)
     * @param highProductInfoModel
     * @param txChannel
     * @return boolean
     * @author: haiguang.chen
     * @date: 2022/5/6 9:49
     * @since JDK 1.8
     */
    private boolean validIsSupCounterOrWeb(HighProductInfoModel highProductInfoModel, String txChannel) {
        String branchCode = highProductInfoModel.getBranchCode();
        if (BranchCodeEnum.ALL.getCode().equals(branchCode)) {
            return true;
        }
        if (TxChannelEnum.COUNTER.getCode().equals(txChannel) && (StringUtils.isEmpty(branchCode) || BranchCodeEnum.UN_COUNTER.getCode().equals(branchCode))) {
            return false;
        }
        if (!TxChannelEnum.COUNTER.getCode().equals(txChannel) && (StringUtils.isEmpty(branchCode) || BranchCodeEnum.COUNTER.getCode().equals(branchCode))) {
            return false;
        }
        return true;
    }

    /**
     * @description:(校验产品可购买类型)
     * @param highProductInfoModel
     * @param invstType
     * @return boolean
     * @author: haiguang.chen
     * @date: 2022/5/6 9:49
     * @since JDK 1.8
     */
    private boolean validIsSupBuyInvstType(HighProductInfoModel highProductInfoModel, String invstType) {
        String buyUserType = highProductInfoModel.getBuyUserType();
        if (StringUtils.isBlank(buyUserType)) {
            return true;
        }
        if (buyUserType.contains(ProductBuyUserTypeEnum.ALL.getCode())) {
            return true;
        }
        String userType = ProductBuyUserTypeEnum.getByInvstType(invstType);
        if (userType==null || !buyUserType.contains(userType)) {
            return false;
        }
        return true;
    }

    /**
     *
     * validateTradeStatus:(验证交易状态校验)
     *
     * @param request
     * @param highProductInfoModel
     * <AUTHOR>
     * @date 2018年5月31日 下午7:20:22
     */
    private boolean validateProductTradeStatus(HighProductInfoModel highProductInfoModel, QueryCanBuyFundsForDsRequest request, String taTradeDt) {

        // 上报TA日
        String submitTaDt = null;
        // 中台业务码
        String mBusiCode = null;
        // 产品支持提前下单标志
        String isScheduledTrade = highProductInfoModel.getIsScheduledTrade();
        String appDtmStr = new StringBuilder(request.getAppDt()).append(request.getAppTm()).toString();
        Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);

        // 根据当前TaTradeDt与募集结束日比较, 得出具体业务码
        // 募集结束日期
        String ipoEndDt = highProductInfoModel.getIpoEndDt();
        if (StringUtils.isEmpty(ipoEndDt)) {
            logger.error("productCode:{},没有配置募集结束日期",highProductInfoModel.getFundCode());
            return false;
        }
        mBusiCode = BusinessCodeEnum.SUBS.getMCode();
        if (taTradeDt.compareTo(ipoEndDt) > 0) {
            mBusiCode = BusinessCodeEnum.PURCHASE.getMCode();
        }

        // 预约信息处理
        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode)
                || (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode) && (IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(isScheduledTrade)
                || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade)))) {
            // 查询预约信息
            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(
                    highProductInfoModel.getFundCode(), "0", highProductInfoModel.getShareClass(), request.getDisCode(), appDtm);
            if (productAppointmentInfoBean == null) {
                logger.error("productCode:{},没有查询到预约日历",highProductInfoModel.getFundCode());
                return false;
            }

            // 交易是否在可购买期
            if(!TxChannelEnum.COUNTER.getCode().equals(request.getTxChannel())
                    && (StringUtils.isNotEmpty(productAppointmentInfoBean.getBuyDay()) && StringUtils.isNotEmpty(productAppointmentInfoBean.getBuyTime()))){
                String buyDateStr = new StringBuilder(productAppointmentInfoBean.getBuyDay()).append(productAppointmentInfoBean.getBuyTime()).toString();
                if(buyDateStr.compareTo(appDtmStr) > 0){
                    logger.error("productCode:{},不在购买期",highProductInfoModel.getFundCode());
                    return false;
                }
            }

            submitTaDt = productAppointmentInfoBean.getOpenEndDt();
        } else {
            submitTaDt = taTradeDt;
        }

        // 查询高端产品状态信息
        HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductInfoModel.getFundCode(), submitTaDt);
        if (StringUtils.isEmpty(mBusiCode)) {
            if (highProductStatInfoBean == null) {
                logger.error("productCode:{},状态不正常",highProductInfoModel.getFundCode());
                return false;
            }

            BusinessCodeEnum businessCodeEnum = ConvertCodeUtils.convertBuyBusiCode(highProductStatInfoBean.getFundStat());
            if (businessCodeEnum == null) {
                logger.error("productCode:{},业务码转换错误",highProductInfoModel.getFundCode());
                return false;
            } else {
                mBusiCode = businessCodeEnum.getMCode();
            }

        }

        // 查询高端产品交易开通配置信息
        HighProductTxOpenCfgBean highProductTxOpenCfgBean = queryHighProductOuterService.getHighProductTxOpenCfg(highProductInfoModel.getFundCode(),
                BusinessCodeEnum.getByMCode(mBusiCode).getCode());

        // 校验产品校验状态
        return valdiateProductStat(highProductInfoModel, highProductStatInfoBean, highProductTxOpenCfgBean, mBusiCode);

    }

    /**
     *
     * valdiateProductStat:(校验产品状态)
     *
     * @param highProductStatInfoBean
     * @param mBusiCode
     * @return
     * <AUTHOR>
     * @date 2018年5月31日 下午7:23:34
     */
    private boolean valdiateProductStat(HighProductInfoModel highProductInfoModel, HighProductStatInfoBean highProductStatInfoBean,
                                                                             HighProductTxOpenCfgBean highProductTxOpenCfgBean, String mBusiCode) {
        if (highProductStatInfoBean == null || StringUtils.isEmpty(highProductStatInfoBean.getFundStat())) {
            logger.error("productCode:{},参数错误",highProductInfoModel.getFundCode());
            return false;
        }

        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) && !MDataDic.CAN_SUR_SET.contains(highProductStatInfoBean.getFundStat())) {
            logger.error("productCode:{},基金状态不可购买",highProductInfoModel.getFundCode());
            return false;
        }
        if (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode) && !MDataDic.CAN_PUR_SET.contains(highProductStatInfoBean.getFundStat())) {
            logger.error("productCode:{},基金状态不可购买",highProductInfoModel.getFundCode());
            return false;
        }

        // 非群济私募产品校验交易开通标识
        if (highProductTxOpenCfgBean == null) {
            logger.error("productCode:{},产品未开通",highProductInfoModel.getFundCode());
            return false;
        }
        if (TxOpenFlagEnum.CLOSE.getCode().equals(highProductTxOpenCfgBean.getOpenFlag())) {
            logger.error("productCode:{},产品未开通",highProductInfoModel.getFundCode());
            return false;
        }

        return true;
    }
}