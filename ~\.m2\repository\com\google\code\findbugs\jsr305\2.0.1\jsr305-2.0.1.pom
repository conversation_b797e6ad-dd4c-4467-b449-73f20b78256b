
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.google.code.findbugs</groupId>
	<artifactId>jsr305</artifactId>
	<version>2.0.1</version>
	<packaging>jar</packaging>
	<url>http://findbugs.sourceforge.net/</url>
	<name>FindBugs-jsr305</name>
	<description>JSR305 Annotations for Findbugs</description>
	<licenses>
		<license>
			<name>The Apache Software License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<scm>
		<connection>scm:svn:http://findbugs.googlecode.com/svn/trunk/</connection>
		<developerConnection>scm:svn:https://findbugs.googlecode.com/svn/trunk/</developerConnection>
		<url>http://findbugs.googlecode.com/svn/trunk/</url>
	</scm>
</project>
