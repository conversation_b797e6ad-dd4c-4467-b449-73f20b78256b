/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.constants.BalanceConstants;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.service.BalanceProcessService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @description: 查询产品持仓接口实现(优化版)
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
@DubboService
@Component
public class QueryAcctBalanceOptimizedFacadeService implements QueryAcctBalanceOptimizedFacade {

    private static final Logger logger = LogManager.getLogger(QueryAcctBalanceOptimizedFacadeService.class);

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private BalanceProcessService balanceProcessService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryAcctBalanceOptimizedFacadeService
     * @apiName execute
     * @apiDescription 查询产品持仓接口实现(优化版)
     * @apiParam (请求参数) {String} hkSaleFlag 好买香港代销标识
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} productType 产品类型
     * @apiParam (请求参数) {String} productSubType 产品子类型
     * @apiParam (请求参数) {String} protocolType 协议类型，4-高端产品协议
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表-股权直销改造
     * @apiParam (请求参数) {String} callType 1-新资产中心，2-老资产中心
     * @apiParam (请求参数) {String} balanceStatus 持仓状态,0:不持仓,1:持仓,2:全部，默认查持仓的
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParamExample 请求参数示例
     * {"hkSaleFlag":"1","productCode":"HM001","productType":"1","productSubType":"01","protocolType":"4","disCodeList":["HM","HZ"],"callType":"2","balanceStatus":"1","notFilterHkFund":"0","notFilterHzFund":"0","txAcctNo":"123456789","hbOneNo":"HB123456","disCode":"HM","outletCode":"001","appDt":"20250815","appTm":"143000","operIp":"***********","txCode":"Z330086","txChannel":"2"}
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} disCode 分销机构代码
     * @apiSuccess (响应结果) {Array} disCodeList 分销机构代码列表
     * @apiSuccess (响应结果) {Number} totalMarketValue 总市值
     * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 在途总金额
     * @apiSuccess (响应结果) {Number} totalUnconfirmedNum 待确认笔数
     * @apiSuccess (响应结果) {Number} redeemUnconfirmedNum 赎回待确认笔数
     * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益
     * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态
     * @apiSuccess (响应结果) {Number} totalCashCollection 总回款
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品
     * @apiSuccess (响应结果) {Array} balanceList 持仓信息列表
     * @apiSuccess (响应结果) {String} balanceList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceList.productName 产品名称
     * @apiSuccess (响应结果) {Number} balanceList.balanceVol 持仓份额
     * @apiSuccess (响应结果) {Number} balanceList.marketValue 市值
     * @apiSuccess (响应结果) {Number} balanceList.currentIncome 当前收益
     * @apiSuccess (响应结果) {Number} balanceList.accumIncome 累计收益
     * @apiSuccess (响应结果) {Array} unconfirmeProducts 在途产品列表
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccessExample 响应结果示例
     * {"txAcctNo":"123456789","disCode":"HM","disCodeList":["HM","HZ"],"totalMarketValue":"10500.00","totalCurrentAsset":"500.00","balanceList":[{"productCode":"HM001","productName":"好买基金1号","balanceVol":"10000.00","marketValue":"10500.00","currentIncome":"500.00"}],"returnCode":"0000","description":"成功"}
     */
    @Override
    public QueryAcctBalanceOptimizedResponse execute(QueryAcctBalanceOptimizedRequest request) {
        logger.info("查询客户持仓信息(优化版)开始，请求参数：{}", JSON.toJSONString(request));

        try {
            // 1.参数预处理
            preprocessRequest(request);

            // 2.构建响应对象
            QueryAcctBalanceOptimizedResponse response = buildInitialResponse(request);

            // 3.参数校验
            String txAcctNo = request.getTxAcctNo();
            String hbOneNo = request.getHbOneNo();
            if (StringUtils.isBlank(txAcctNo) && StringUtils.isBlank(hbOneNo)) {
                logger.warn("查询持仓，交易账号和一账通号都为空，返回空结果");
                return response;
            }

            // 4.获取分销机构列表
            List<String> disCodeList = getDisCodeList(request);

            // 5.查询清盘产品列表
            List<String> crisisFundList = getCrisisFundList();

            // 6.持仓数据处理
            List<OptimizedBalanceBean> balanceList = new ArrayList<>();
            response.setBalanceList(balanceList);

            // 7.处理代销资产
            balanceProcessService.processConsignmentBalance(request, txAcctNo, hbOneNo, balanceList, crisisFundList);

            // 8.处理直销资产
            balanceProcessService.processDirectBalance(request, txAcctNo, hbOneNo, balanceList, crisisFundList);

            // 9.过滤持仓信息
            balanceProcessService.filterBalanceInfo(response, balanceList, request);

            // 10.汇总处理
            balanceProcessService.processTotalSummary(response, balanceList, request);

            // 11.获取在途资产
            balanceProcessService.processOnWayAssets(request, disCodeList, txAcctNo, response);

            // 12.排序
            balanceProcessService.sortBalanceList(balanceList);

            logger.info("查询客户持仓信息(优化版)成功，返回{}条持仓记录", balanceList.size());
            return response;

        } catch (Exception e) {
            logger.error("查询客户持仓信息(优化版)异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * @description: 参数预处理
     * @param request 请求参数
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private void preprocessRequest(QueryAcctBalanceOptimizedRequest request) {
        // 默认只查持仓的
        if (StringUtils.isBlank(request.getBalanceStatus())) {
            request.setBalanceStatus(BalanceConstants.BalanceStatus.HAS_BALANCE);
        }

        // 处理授权相关参数
        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }
    }

    /**
     * @description: 构建初始响应对象
     * @param request 请求参数
     * @return 响应对象
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private QueryAcctBalanceOptimizedResponse buildInitialResponse(QueryAcctBalanceOptimizedRequest request) {
        QueryAcctBalanceOptimizedResponse response = new QueryAcctBalanceOptimizedResponse();
        response.setTxAcctNo(request.getTxAcctNo());
        response.setDisCode(request.getDisCode());
        response.setDisCodeList(getDisCodeList(request));
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        return response;
    }

    /**
     * @description: 获取分销机构列表
     * @param request 请求参数
     * @return 分销机构列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private List<String> getDisCodeList(QueryAcctBalanceOptimizedRequest request) {
        if (request.getDisCodeList() != null && !request.getDisCodeList().isEmpty()) {
            return request.getDisCodeList();
        }
        return Arrays.asList(DisCodeEnum.HM.getCode());
    }

    /**
     * @description: 获取清盘产品列表
     * @return 清盘产品列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private List<String> getCrisisFundList() {
        // TODO: 实现获取清盘产品列表的逻辑
        // 这里应该调用相应的服务获取清盘产品列表
        return new ArrayList<>();
    }
}
