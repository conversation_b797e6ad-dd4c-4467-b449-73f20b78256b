/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.client.domain.BalanceBeanVO;
import com.howbuy.dtms.order.client.domain.UnconfirmeProductVO;
import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryBalanceRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceFacade;
import com.howbuy.interlayer.product.model.HighProductDBInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 查询用户持仓,海外的转包海外接口,其他的查询之前的接口
 */
@DubboService
@Service("queryAcctBalanceNewFacade")
@Slf4j
public class QueryAcctBalanceFacadeNewService implements QueryAcctBalanceNewFacade {
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;
    @Autowired
    private QueryBalanceFacade queryBalanceFacade;
    @Value("${query.balance.by.old:0}")
    private String queryBalanceByOld;
    @Value("${query.balance.by.old.hboneNos}")
    private String queryBalanceByOldHboneNos;
    /**
     * HK成功的返回编码
     */
    public static final String HK_SUCCESS_CODE = "0000";

    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceRequest request) {
        log.info("QueryAcctBalanceNewFacade-查询新持仓接口,request={}", JSON.toJSONString(request));
        if (YesOrNoEnum.YES.getCode().equals(queryBalanceByOld)) {
            log.info("持仓接口,切开关,只查询老接口,queryBalanceByOld={}", queryBalanceByOld);
            return queryAcctBalanceFacade.execute(request);
        }
        if (StringUtils.isBlank(request.getHbOneNo()) || StringUtils.isNotBlank(queryBalanceByOldHboneNos) && queryBalanceByOldHboneNos.contains(request.getHbOneNo())) {
            log.info("持仓接口,白名单用户或者一账通为空,只查询老接口,queryBalanceByOldHboneNos={}", queryBalanceByOldHboneNos);
            return queryAcctBalanceFacade.execute(request);
        }
        // 是否只需要查询香港产品
        Boolean hkProduct = null;
        if (StringUtils.isNotBlank(request.getProductCode())) {
            hkProduct = isHkProduct(request.getProductCode());
        }
        if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.YES.getCode().equals(request.getHkSaleFlag())) {
            hkProduct = true;
        } else if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.NO.getCode().equals(request.getHkSaleFlag())) {
            hkProduct = false;
        }
        String notFilterHkFund = request.getNotFilterHkFund();
        // 1.查询非海外的持仓
        QueryAcctBalanceResponse unHkBalance = queryUnHkAcctBalance(request, hkProduct);
        // 2.查询海外持仓
        QueryBalanceResponse hkBalance = queryHkBalance(request, hkProduct);
        // 3.数据汇总
        return buildTotalBalanceInfo(unHkBalance, hkBalance, notFilterHkFund);
    }

    /***
     * 设置汇总数据
     * @param unHkBalance 非香港持仓
     * @param hkBalance 香港持仓
     */
    private QueryAcctBalanceResponse buildTotalBalanceInfo(QueryAcctBalanceResponse unHkBalance, QueryBalanceResponse hkBalance, String notFilterHkFund) {
        QueryAcctBalanceResponse totalBalance = new QueryAcctBalanceResponse();
        totalBalance.setReturnCode(unHkBalance.getReturnCode());
        totalBalance.setDescription(unHkBalance.getDescription());
        // 3.0.是否有海外产品
        if (CollectionUtils.isNotEmpty(hkBalance.getBalanceList())) {
            totalBalance.setHasHKProduct(YesOrNoEnum.YES.getCode());
        } else {
            totalBalance.setHasHKProduct(YesOrNoEnum.NO.getCode());
        }
        // 如果需要过滤香港产品,这里就直接将香港产品赋值为空
        if (YesOrNoEnum.NO.getCode().equals(notFilterHkFund)) {
            hkBalance = new QueryBalanceResponse();
        }
        // 3.1.总市值
        BigDecimal unHkTotalMarketValue = unHkBalance.getTotalMarketValue() == null ? BigDecimal.ZERO : unHkBalance.getTotalMarketValue();
        BigDecimal hkTotalMarketValue = hkBalance.getTotalMarketValue() == null ? BigDecimal.ZERO : hkBalance.getTotalMarketValue();
        totalBalance.setTotalMarketValue(unHkTotalMarketValue.add(hkTotalMarketValue));
        // 3.2.总未确认金额
        BigDecimal unHkTotalUnconfirmedAmt = unHkBalance.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : unHkBalance.getTotalUnconfirmedAmt();
        BigDecimal hkTotalUnconfirmedAmt = hkBalance.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : hkBalance.getTotalUnconfirmedAmt();
        totalBalance.setTotalUnconfirmedAmt(unHkTotalUnconfirmedAmt.add(hkTotalUnconfirmedAmt));
        // 3.3.总未确认数量
        int unHkTotalUnconfirmedNum = unHkBalance.getTotalUnconfirmedNum() == null ? 0 : unHkBalance.getTotalUnconfirmedNum();
        int hkTotalUnconfirmedNum = hkBalance.getTotalUnconfirmedNum() == null ? 0 : hkBalance.getTotalUnconfirmedNum();
        totalBalance.setTotalUnconfirmedNum(unHkTotalUnconfirmedNum + hkTotalUnconfirmedNum);
        // 3.4.赎回未确认数量
        int unHkRedeemUnconfirmedNum = unHkBalance.getRedeemUnconfirmedNum() == null ? 0 : unHkBalance.getRedeemUnconfirmedNum();
        int hkRedeemUnconfirmedNum = hkBalance.getRedeemUnconfirmedNum() == null ? 0 : hkBalance.getRedeemUnconfirmedNum();
        totalBalance.setRedeemUnconfirmedNum(unHkRedeemUnconfirmedNum + hkRedeemUnconfirmedNum);
        // 3.5.当前总收益
        BigDecimal unHkTotalCurrentAsset = unHkBalance.getTotalCurrentAsset() == null ? BigDecimal.ZERO : unHkBalance.getTotalCurrentAsset();
        BigDecimal hkTotalCurrentAsset = hkBalance.getTotalCurrentAsset() == null ? BigDecimal.ZERO : hkBalance.getTotalCurrentAsset();
        totalBalance.setTotalCurrentAsset(unHkTotalCurrentAsset.add(hkTotalCurrentAsset));
        // 3.6.总收益计算状态: 0-计算中;1-计算成功
        String unHkTotalIncomCalStat = unHkBalance.getTotalIncomCalStat() == null ? YesOrNoEnum.YES.getCode() : unHkBalance.getTotalIncomCalStat();
        // 海外没有该字段,都是计算成功
        totalBalance.setTotalIncomCalStat(YesOrNoEnum.NO.getCode().equals(unHkTotalIncomCalStat) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        // 3.7.总回款
        BigDecimal unHkTotalCashCollection = unHkBalance.getTotalCashCollection() == null ? BigDecimal.ZERO : unHkBalance.getTotalCashCollection();
        BigDecimal hkTotalCashCollection = hkBalance.getTotalCashCollection() == null ? BigDecimal.ZERO : hkBalance.getTotalCashCollection();
        totalBalance.setTotalCashCollection(unHkTotalCashCollection.add(hkTotalCashCollection));

        // 3.9.是否有好臻产品
        totalBalance.setHasHZProduct(unHkBalance.getHasHZProduct());
        // 3.10.总未确认产品列表
        List<UnconfirmeProduct> unConfirmedProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unHkBalance.getUnconfirmeProducts())) {
            unConfirmedProducts.addAll(unHkBalance.getUnconfirmeProducts());
        }
        if (CollectionUtils.isNotEmpty(hkBalance.getUnconfirmeProducts())) {
            for (UnconfirmeProductVO unConfirmedProduct : hkBalance.getUnconfirmeProducts()) {
                UnconfirmeProduct unconfirmeProduct = new UnconfirmeProduct();
                BeanUtils.copyProperties(unConfirmedProduct, unconfirmeProduct);
                unConfirmedProducts.add(unconfirmeProduct);
            }
        }
        totalBalance.setUnconfirmeProducts(unConfirmedProducts);
        // 3.11.持仓信息汇总
        List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList = new ArrayList<>();
        List<QueryAcctBalanceResponse.BalanceBean> unHkBalanceList = unHkBalance.getBalanceList();
        if (CollectionUtils.isNotEmpty(unHkBalanceList)) {
            totalBalanceList.addAll(unHkBalanceList);
        }
        List<BalanceBeanVO> hkBalanceList = hkBalance.getBalanceList();
        if (CollectionUtils.isNotEmpty(hkBalanceList)) {
            for (BalanceBeanVO balanceBeanVO : hkBalanceList) {
                QueryAcctBalanceResponse.BalanceBean balanceBean = new QueryAcctBalanceResponse.BalanceBean();
                BeanUtils.copyProperties(balanceBeanVO, balanceBean);
                balanceBean.setAbnormalFlag(YesOrNoEnum.NO.getCode());
                balanceBean.setPaidInAmt(balanceBeanVO.getSubTotalAmt());
                balanceBean.setPaidTotalAmt(balanceBeanVO.getPaidTotalAmt());
                balanceBean.setPaidSubTotalRatio(balanceBeanVO.getPaidSubTotalRatio());
                // 是否分次call
                balanceBean.setFractionateCallFlag(balanceBeanVO.getFractionateCallFlag());
                totalBalanceList.add(balanceBean);
            }
        }
        totalBalance.setBalanceList(totalBalanceList);
        return totalBalance;
    }

    /**
     * 判断产品是否是香港产品
     *
     * @param fundCode 产品编码
     * @return 是否香港产品, true-是香港产品, false-不是香港产品
     */
    public boolean isHkProduct(String fundCode) {
        List<HighProductDBInfoModel> highProductInfoList = highProductService.getHighProductDBInfo(Collections.singletonList(fundCode));
        if (CollectionUtils.isEmpty(highProductInfoList)) {
            log.info("isHkProduct-根据产品编码查询产品信息为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        String hkSaleFlag = highProductInfoList.get(0).getHkSaleFlag();
        if (StringUtils.isBlank(hkSaleFlag)) {
            log.info("isHkProduct-根据产品编码查询产品信息hkSaleFlag为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        return YesOrNoEnum.YES.getCode().equals(hkSaleFlag);
    }


    /**
     * 查询高端用户持仓信息
     */
    public QueryAcctBalanceResponse queryUnHkAcctBalance(QueryAcctBalanceRequest request, Boolean hkProduct) {
        log.info("queryUnHkAcctBalance-查询非香港持仓接口,queryBalanceParamCmd={}", JSON.toJSONString(request));
        if (hkProduct != null && hkProduct) {
            log.info("查询持仓的产品有值,而且是香港产品,不需要查询非香港持仓接口");
            QueryAcctBalanceResponse queryAcctBalanceResponse = new QueryAcctBalanceResponse();
            queryAcctBalanceResponse.setReturnCode(ExceptionCodes.SUCCESS);
            return queryAcctBalanceResponse;
        }
        log.info("queryUnHkAcctBalance-查询非海外持仓接口-start,queryAcctBalanceRequest={}", JSON.toJSONString(request));
        // 查询非海外持仓固定过滤掉香港的
        request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
        QueryAcctBalanceResponse response = queryAcctBalanceFacade.execute(request);
        log.info("queryUnHkAcctBalance-查询非海外持仓接口-结果,response={}", JSON.toJSONString(response));
        return response;
    }

    /**
     * 香港数据查询
     *
     * @param request 查询持仓入参
     * @return 香港持仓数据
     */
    public QueryBalanceResponse queryHkBalance(QueryAcctBalanceRequest request, Boolean hkProduct) {
        if (hkProduct != null && !hkProduct) {
            log.info("queryHkBalance-不是香港产品,不查询香港持仓");
            return new QueryBalanceResponse();
        }
        if (StringUtils.isBlank(request.getHbOneNo())) {
            log.info("queryHkBalance-没有一账通号,不查询海外持仓接口");
            return new QueryBalanceResponse();
        }
        QueryBalanceRequest queryBalanceRequest = new QueryBalanceRequest();
        queryBalanceRequest.setHbOneNo(request.getHbOneNo());
        queryBalanceRequest.setFundCode(request.getProductCode());
        queryBalanceRequest.setCurrencyDisType("156");
        // 兼容老逻辑,如果传5,说明查询好臻的
        if ("5".equals(request.getProductSubType()) && YesOrNoEnum.NO.getCode().equals(request.getHkSaleFlag())) {
            queryBalanceRequest.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        } else {
            if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
                queryBalanceRequest.setDisCodeList(request.getDisCodeList());
            } else if (StringUtils.isNotBlank(request.getDisCode())) {
                queryBalanceRequest.setDisCodeList(Collections.singletonList(request.getDisCode()));
            } else {
                queryBalanceRequest.setDisCodeList(request.getDisCodeList());
            }
        }
        queryBalanceRequest.setBalanceStatus(request.getBalanceStatus());
        log.info("queryHkBalance-查询海外持仓接口-start,queryBalanceRequest={}", JSON.toJSONString(queryBalanceRequest));
        Response<QueryBalanceResponse> hkBalanceResponse = queryBalanceFacade.execute(queryBalanceRequest);
        log.info("queryHkBalance-查询海外持仓接口-结果:hkBalanceResponse={}", JSON.toJSONString(hkBalanceResponse));
        if (!HK_SUCCESS_CODE.equals(hkBalanceResponse.getCode()) || hkBalanceResponse.getData() == null) {
            log.info("queryHkBalance-海外持仓接口,返回非成功状态,当做没有海外持仓处理");
            return new QueryBalanceResponse();
        }
        return hkBalanceResponse.getData();
    }


}
