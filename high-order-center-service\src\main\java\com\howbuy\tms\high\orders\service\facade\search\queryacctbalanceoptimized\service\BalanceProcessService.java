/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.service;

import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean;

import java.util.List;

/**
 * @description: 持仓业务处理服务
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
public interface BalanceProcessService {

    /**
     * @description: 处理代销资产
     * @param request 请求参数
     * @param txAcctNo 交易账号
     * @param hbOneNo 一账通号
     * @param balanceList 持仓列表
     * @param crisisFundList 清盘产品列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    void processConsignmentBalance(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                                   List<OptimizedBalanceBean> balanceList, List<String> crisisFundList);

    /**
     * @description: 处理直销资产
     * @param request 请求参数
     * @param txAcctNo 交易账号
     * @param hbOneNo 一账通号
     * @param balanceList 持仓列表
     * @param crisisFundList 清盘产品列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    void processDirectBalance(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                              List<OptimizedBalanceBean> balanceList, List<String> crisisFundList);

    /**
     * @description: 汇总处理
     * @param response 响应对象
     * @param balanceList 持仓列表
     * @param request 请求参数
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    void processTotalSummary(QueryAcctBalanceOptimizedResponse response, List<OptimizedBalanceBean> balanceList,
                             QueryAcctBalanceOptimizedRequest request);

    /**
     * @description: 过滤持仓信息
     * @param response 响应对象
     * @param balanceList 持仓列表
     * @param request 请求参数
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    void filterBalanceInfo(QueryAcctBalanceOptimizedResponse response, List<OptimizedBalanceBean> balanceList,
                           QueryAcctBalanceOptimizedRequest request);

    /**
     * @description: 获取在途资产
     * @param request 请求参数
     * @param disCodeList 分销机构列表
     * @param txAcctNo 交易账号
     * @param response 响应对象
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    void processOnWayAssets(QueryAcctBalanceOptimizedRequest request, List<String> disCodeList, String txAcctNo,
                            QueryAcctBalanceOptimizedResponse response);

    /**
     * @description: 排序持仓列表
     * @param balanceList 持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    void sortBalanceList(List<OptimizedBalanceBean> balanceList);
}
