<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.acc</groupId>
	<artifactId>acc-common</artifactId>
	<version>3.5.9-RELEASE</version>
	<packaging>pom</packaging>
	<name>acc-common</name>
	<description>账户中心公共工程</description>
	<issueManagement>
		<system>JIRA</system>
		<url>http://ims.intelnal.howbuy.com/</url>
	</issueManagement>
	<repositories>
		<repository>
			<id>ReleaseRepo</id>
			<url>http://nexus.howbuy.pa/repository/public/</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>public-plugin</id>
			<url>http://nexus.howbuy.pa/repository/public/</url>
		</pluginRepository>
	</pluginRepositories>

	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>howbuy-releases</name>
			<url>http://nexus.howbuy.pa/repository/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>howbuy-snapshots</name>
			<url>http://nexus.howbuy.pa/repository/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
	<properties>
		
		<java.version>1.6</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<junit.version>4.12</junit.version>
		<lombok.version>1.18.20</lombok.version>
		<com.howbuy.common.version>1.0.0-SNAPSHOT</com.howbuy.common.version>
		<org.slf4j.version>1.7.4</org.slf4j.version>
		<fastjson.version>1.1.41</fastjson.version>
		<org.jodd.version>3.5.2</org.jodd.version>
		<zookeeper.version>3.4.6</zookeeper.version>
		<pagehelper.version>3.6.3</pagehelper.version>
		<commons.lang.version>2.2</commons.lang.version>
		<guava.version>19.0</guava.version>
		<com.howbuy.acc-common.version>3.5.9-RELEASE</com.howbuy.acc-common.version>
		<com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-service</artifactId>
				<version>${com.howbuy.common-service.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<version>${org.slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>${zookeeper.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.sun.jmx</groupId>
						<artifactId>jmxri</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.sun.jdmk</groupId>
						<artifactId>jmxtools</artifactId>
					</exclusion>
					<exclusion>
						<groupId>javax.jms</groupId>
						<artifactId>jms</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>5.2.15.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>org.jodd</groupId>
				<artifactId>jodd-vtor</artifactId>
				<version>${org.jodd.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>${commons.lang.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acc</groupId>
				<artifactId>acc-common-cache</artifactId>
				<version>${com.howbuy.acc-common.version}</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cachemanagement</artifactId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<artifactId>servlet-api</artifactId>
						<groupId>javax.servlet</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>${com.howbuy.howbuy-ccms-watcher.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.4</version>
				<executions>
					<execution>
						<id>attach-source</id>
						<phase>install</phase>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>