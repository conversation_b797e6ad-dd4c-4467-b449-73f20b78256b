/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.TxCodes;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 查询客户持仓请求(优化版)
 * @author: hong<PERSON>.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryAcctBalanceOptimizedRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 1L;

    public QueryAcctBalanceOptimizedRequest() {
        setTxCode(TxCodes.HIGH_FUND_QUERY_ACCT_BALANCE_OPTIMIZED);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 好买香港代销标识
     */
    private String hkSaleFlag;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品子类型
     */
    private String productSubType;

    /**
     * 协议类型
     * 4-高端产品协议
     */
    private String protocolType = "4";

    /**
     * 分销机构代码列表
     * 股权直销改造
     */
    private List<String> disCodeList;

    /**
     * 调用类型
     * 1-新资产中心，2-老资产中心
     */
    private String callType = "2";

    /**
     * 持仓状态
     * 0-不持仓，1-持仓，2-全部，默认查持仓
     */
    private String balanceStatus = "1";

    /**
     * 不过滤香港产品
     * 1-是，0-否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品
     * 1-是，0-否
     */
    private String notFilterHzFund;

    /**
     * 是否授权,默认是按照已授权查询所有数据
     */
    @Deprecated
    private String isAuth;
}
