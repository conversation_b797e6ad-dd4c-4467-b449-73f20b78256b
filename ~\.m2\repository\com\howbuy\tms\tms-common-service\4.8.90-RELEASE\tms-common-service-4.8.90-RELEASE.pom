<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.tms</groupId>
	<artifactId>tms-common-service</artifactId>
	<version>4.8.90-RELEASE</version>
	<packaging>jar</packaging>
	<name>tms-common-service</name>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<java.version>1.8</java.version>
		<activemq.version>5.10.2</activemq.version>
		<spring.version>3.2.16.RELEASE</spring.version>
		<log4j.version>2.17.0</log4j.version>
		<aspectj.version>1.6.8</aspectj.version>
		<ccms.version>1.0-SNAPSHOT</ccms.version>
		<dubbo.version>2.5.3</dubbo.version>
		<com.howbuy.tms-common-lang.version>4.8.90-RELEASE</com.howbuy.tms-common-lang.version>
		<com.howbuy.tms-common-client.version>4.8.90-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.tms-common-enums.version>4.8.90-RELEASE</com.howbuy.tms-common-enums.version>
		<com.howbuy.howbuy-cachemanagement.version>3.8.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-trace.version>1.0.5-RELEASE</com.howbuy.howbuy-trace.version>
</properties>
	
	<dependencies>

		<dependency>
			<groupId>com.howbuy</groupId>
        	<artifactId>utils</artifactId>
        	<version>1.0.0-SNAPSHOT</version>
        	<exclusions>
        		<exclusion>
        			<groupId>org.springframework</groupId>
        			<artifactId>spring-beans</artifactId>
        		</exclusion>
        	</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-lang</artifactId>
			<version>${com.howbuy.tms-common-lang.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-client</artifactId>
			<version>${com.howbuy.tms-common-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
			<version>${com.howbuy.tms-common-enums.version}</version>
		</dependency>
		
		<!-- log4j2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		
		<!--用于与slf4j保持桥接-->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<!-- log4j2 -->
		
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.11</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
		</dependency>
		
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>${aspectj.version}</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>${aspectj.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.trilead</groupId>
			<artifactId>trilead-ssh2</artifactId>
			<version>1.0.0-build217</version>
		</dependency>
		
		<!-- ccms -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-ccms-independent</artifactId>
			<version>1.0.0-release</version>
		</dependency>
		
		<dependency>
			<groupId>net.unicon.springframework</groupId>
			<artifactId>springframework-addons</artifactId>
			<version>${ccms.version}</version>
		</dependency>
		<!-- ccms -->
		
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
			<version>${com.howbuy.howbuy-cachemanagement.version}</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>4.3.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.11.1</version>
		</dependency>
		
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dubbo</artifactId>
			<version>${dubbo.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>spring</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.2</version>
		</dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.12</version>
            <scope>compile</scope>
        </dependency>

		<!-- trace -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-trace</artifactId>
			<version>${com.howbuy.howbuy-trace.version}</version>
		</dependency>
    </dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Java源码插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.4</version>
				<executions>
					<execution>
						<id>attach-source</id>
						<phase>install</phase>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
</project>