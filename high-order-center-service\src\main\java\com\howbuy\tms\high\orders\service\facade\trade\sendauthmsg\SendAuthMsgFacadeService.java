/**
 * Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.trade.sendauthmsg;

import com.howbuy.tms.cache.service.hithauthcache.HighAuthCacheService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.AuthMsgTypeEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.convenientvrifyformobile.ConvenientVrifyForMobileOuterService;
import com.howbuy.tms.common.outerservice.auth.encryptsingle.EncryptSingleOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.SendMessageOuterService;
import com.howbuy.tms.common.outerservice.es.authentication.GetMobileVerificationCodeOuterService;
import com.howbuy.tms.common.outerservice.payonline.quickcardauth.QuickCardAuthOuterOuterService;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgFacade;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgRequest;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgResponse;
import com.howbuy.tms.high.orders.service.business.task.SendAuthMsgTask;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:(发送鉴权信息)
 * @reason:
 * <AUTHOR>
 * @date 2018年8月31日 下午9:21:43
 * @since JDK 1.6
 */
@DubboService
@Service("sendAuthMsgFacade")
public class SendAuthMsgFacadeService implements SendAuthMsgFacade {
    @Autowired
    private HighAuthCacheService highAuthCacheService;

    @Autowired
    private QuickCardAuthOuterOuterService quickCardAuthOuterOuterService;

    @Autowired
    private ConvenientVrifyForMobileOuterService convenientVrifyForMobileOuterService;

    @Autowired
    private SendMessageOuterService sendMessageOuterService;

    @Autowired
    private GetMobileVerificationCodeOuterService getMobileVerificationCodeOuterService;

    @Autowired
    private EncryptSingleOuterService encryptSingleOuterService;


    @Override
    public SendAuthMsgResponse execute(SendAuthMsgRequest request) {
        SendAuthMsgResponse response = new SendAuthMsgResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));

        // 鉴权类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝
        // 好买发送短信，校验手机号是否好买预留
        if (AuthMsgTypeEnum.HOWBUY_MSG_TYPE.getCode().equals(request.getAuthMsgType()) && !request.isMobileExit()) {
            response.setReturnCode(ExceptionCodes.HIGH_ORDER_HOWBUY_MOBILE_NOT_EXIST);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_HOWBUY_MOBILE_NOT_EXIST));
            return response;
        }

        // 异步发送鉴权短信->改为同步调用：需获取实际发送渠道 20230131
        SendAuthMsgTask sendAuthMsgTask = new SendAuthMsgTask(highAuthCacheService, quickCardAuthOuterOuterService, convenientVrifyForMobileOuterService,
                sendMessageOuterService, getMobileVerificationCodeOuterService, request, encryptSingleOuterService);
        sendAuthMsgTask.start();
        return response;

    }


}

