/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryhighdealorderdtl;

import cn.hutool.core.collection.CollectionUtil;
import com.howbuy.common.date.DateUtil;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.TxPmtFlagEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.CanCancelFlagEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.AdvanceFlagEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.enums.database.OrderStatusEnum;
import com.howbuy.tms.common.enums.database.PayStatusEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighConfirmAndPaymentReceiptDtBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.context.HighConfirmAndPaymentReceiptParamContext;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MergeSubmitUtils;
import com.howbuy.tms.high.orders.dao.po.HighRedeemSplitDtlPo;
import com.howbuy.tms.high.orders.dao.po.HzOriginalOrderHisRecordPo;
import com.howbuy.tms.high.orders.dao.vo.QueryHighDealOrderDtlVo;
import com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.bean.HighRedeemSplitDtlBean;
import com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.bean.SubOrderBean;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.business.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.HighRedeemSplitDtlRepository;
import com.howbuy.tms.high.orders.service.repository.HzOriginalOrderHisRecordRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(查询高端交易订单明细service)
 * @reason:
 * @date 2017年4月7日 下午6:06:13
 * @since JDK 1.7
 */
@DubboService
@Service("queryHighDealOrderDtlFacade")
public class QueryHighDealOrderDtlFacadeService extends AbstractBusiProcess implements QueryHighDealOrderDtlFacade {
    private static final Logger logger = LogManager.getLogger(QueryHighDealOrderDtlFacadeService.class);

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private HighRedeemSplitDtlRepository highRedeemSplitDtlRepository;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private HzOriginalOrderHisRecordRepository hzOriginalOrderHisRecordRepository;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlFacade.execute()
     * @apiName 查询高端交易订单明细接口
     * @apiGroup high-order-center
     * @apiDescription 查询高端交易订单明细接口
     * @apiParam (req) {String{1..16}} disCode 分销机构代码 eg.HB000A001
     * @apiParam (req) {String{1..9}} outletCode 网点代码 eg.W20170215
     * @apiParam (req) {String{yyyyMMdd}} appDt 申请日期
     * @apiParam (req) {String{HHmmss}} appTm 申请时间
     * @apiParam (req) {Number} pageNo 页码，默认为1
     * @apiParam (req) {Number} pageSize 每页记录数，默认为20
     * @apiParam (req) {String{1..128}} operIp 交易Ip
     * @apiParam (req) {String{1..7}} txCode 交易码，默认为Z300000
     * @apiParam (req) {String{1}} txChannel 交易渠道 1-柜台;2-网站;3-电话;4-Wap;5-App;6-机构
     * @apiParam (req) {String{1..128}} dataTrack 数据跟踪
     * @apiParam {String} dealNo 订单号
     * @apiParamExample {json} Request Example
     * {
     * "disCode" : "HB000A001",
     * "outletCode" : "W20170215",
     * "appDt" : "20170601",
     * "appTm" : "143545",
     * "txCode" : "Z300000",
     * "txChannel" : "1",
     * "dealNo" : "1017041316512500002002155"
     * }
     * @apiSuccess {String} dealNo 客户订单号
     * @apiSuccess {String} fundCode 基金代码
     * @apiSuccess {String} fundName 基金名称
     * @apiSuccess {String} fundType 基金类型
     * @apiSuccess {String} fundShareClass 基金份额类型
     * @apiSuccess {Number} appAmt 申请金额
     * @apiSuccess {Number} appVol 申请份额
     * @apiSuccess {String} redeemDirection 赎回去向<br>0-银行卡;1-储蓄罐
     * @apiSuccess {Number} discountRate 费用折扣率
     * @apiSuccess {String} txAppFlag 订单申请标记
     * @apiSuccess {String} fundDivMode 基金分红方式<br>0-红利再投;1-现金红利
     * @apiSuccess {String} mBusiCode 中台业务代码
     * @apiSuccess {Number} fee 手续费
     * @apiSuccess {Number} ackAmt 确认金额
     * @apiSuccess {Number} ackVol 确认份额
     * @apiSuccess {String} ackDt 确认日期
     * @apiSuccess {String} taTradeDt TA交易日期
     * @apiSuccess {String} memo 备注
     * @apiSuccess {Number} nav 净值
     * @apiSuccess {Number} appointmentDiscount 预约折扣
     * @apiSuccess {Date} payTime 付款时间
     * @apiSuccess {String} bankAcct 银行卡
     * @apiSuccess {String} bankCode 银行代码
     * @apiSuccess {String} bankName 银行名称
     * @apiSuccess {String} payStatus 付款状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
     * @apiSuccess {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
     * @apiSuccess {String} confirmDt 确认时间
     * @apiSuccess {String} paymentReceiptDt 赎回到款日期
     * @apiSuccess {String} pigReceiptDt 储蓄罐到款日期
     * @apiSuccess {String} txCode 中台交易码
     * @apiSuccess {String} appDt 申请日期
     * @apiSuccess {String} appTm 申请时间
     * @apiSuccess {String} paymentType 支付方式<br>01-自划款;04-代扣款;06-储蓄罐
     * @apiSuccess {Number} esitmateFee 预估手续费
     * @apiSuccess {Number} achievementPay 业绩报酬
     * @apiSuccess {String} firstBuyFlag 首次购买标识<br>1-首次购买;2-追加购买
     * @apiSuccess {String} contractNo 群济系统交易订单号
     * @apiSuccess {String} scaleType 销售类型<br>1-直销;2-代销
     * @apiSuccess {String} productChannel 产品通道<br>1-好买创新;2-创昱达;3-群济;4-好买储蓄罐;5-好买公募
     * @apiSuccess {Date} appDtm 下单时间
     * @apiSuccess {Number} preAppVol 预申请份额
     * @apiSuccess {String} canCancel 是否可撤单0-否 1-是
     * @apiSuccess {String} pmtDt 支付日期
     * @apiSuccess {String} preBookState 预约单状态,预约状态（1-未确认、2-已确认、4-已撤销）
     * @apiSuccess {String} suportPiggyAppointPay 是否支付储蓄罐预约支付0-否1-是
     * @apiSuccessExample {json} Response Example
     * {
     * "dealNo": "1017041316512500002002155",
     * "fundCode": "481001",
     * "fundName": "工银瑞信核心价值混合型证券投资基金",
     * "fundType": "7",
     * "fundShareClass": "A",
     * "appAmt": 5555,
     * "appVol": 0,
     * "redeemDirection": "0",
     * "discountRate": 0.4,
     * "txAppFlag": "1",
     * "fundDivMode": "0",
     * "mBusiCode": "1122",
     * "fee": 0,
     * "ackAmt": 0,
     * "ackVol": 0,
     * "ackDt": "",
     * "taTradeDt": "********",
     * "memo": "",
     * "nav": 0,
     * "appointmentDiscount": 0,
     * "payTime": *************,
     * "bankAcct": "",
     * "bankCode": "",
     * "bankName": "",
     * "payStatus": "1",
     * "orderStatus": "",
     * "confirmDt": "",
     * "paymentReceiptDt": "",
     * "pigReceiptDt": "",
     * "appDt": "********",
     * "appTm": "********",
     * "paymentType": "",
     * "esitmateFee": 0,
     * "achievementPay": 0,
     * "firstBuyFlag": "",
     * "contractNo": "",
     * "scaleType": "",
     * "productChannel": "",
     * "appDtm": ""
     * }
     */
    @Override
    public QueryHighDealOrderDtlResponse execute(QueryHighDealOrderDtlRequest request) {
        QueryHighDealOrderDtlResponse response = new QueryHighDealOrderDtlResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        // 查询订单详情信息
        QueryHighDealOrderDtlVo queryHighDealOrderDtlVo = highDealOrderDtlRepository.selectHighDealOrderDtlByDealNo(request.getDealNo());
        if (queryHighDealOrderDtlVo == null) {
            return response;
        }
        // crm的申请时间可能为空,需要申请时间需要转换下
        setAppTime(queryHighDealOrderDtlVo);
        // 如果有子订单,赋值
        buildSubDealInfo(request, response, queryHighDealOrderDtlVo);
        // 根据资金账号查询银行卡号
        if (!BusinessCodeEnum.DIV_MODE_CHANGE.getMCode().equals(queryHighDealOrderDtlVo.getmBusiCode())) {
            QueryCustBankCardResult result = getBankCardInfo(queryHighDealOrderDtlVo.getCpAcctNo(), request);
            if (result != null) {
                response.setBankAcct(result.getBankAcct());
                response.setBankCode(result.getBankCode());
                response.setBankName(result.getBankName());
            }
        }
        // 多卡交易处理
        processMultiCardInfo(queryHighDealOrderDtlVo, request, response);
        String taTradeDt = queryHighDealOrderDtlVo.getTaTradeDt();
        String fundCode = queryHighDealOrderDtlVo.getFundCode();
        if (!StringUtils.isEmpty(taTradeDt) && !StringUtils.isEmpty(fundCode)
                && ScaleTypeEnum.CONSIGNMENT.getCode().equals(queryHighDealOrderDtlVo.getScaleType())) {
            String busiCode = BusinessCodeEnum.getByMCode(queryHighDealOrderDtlVo.getmBusiCode()).getCode();

            HighConfirmAndPaymentReceiptDtBean highConfirmAndPaymentReceiptDtBean = getHighConfirmAndPaymentReceiptDt(queryHighDealOrderDtlVo.getFundCode(), busiCode, null, queryHighDealOrderDtlVo.getRedeemDirection());

            if (highConfirmAndPaymentReceiptDtBean != null) {
                response.setConfirmDt(highConfirmAndPaymentReceiptDtBean.getConfirmDt());
                response.setPaymentReceiptDt(highConfirmAndPaymentReceiptDtBean.getPaymentReceiptDt());
                response.setPigReceiptDt(highConfirmAndPaymentReceiptDtBean.getPigReceiptDt());
            }
        }
        processAdvanceOrder(response);
        // 撤单标示
        response.setCanCancel(calCancelFlag(queryHighDealOrderDtlVo).getCode());
        // 是否冻结支付
        setSuportPiggyAppointPay(queryHighDealOrderDtlVo, response);
        // 设置非交易的productSubType
        setProductSubType(response);
        // 是否是首次实缴
        hzOrderInfoSet(request, response, queryHighDealOrderDtlVo);
        return response;
    }

    /**
     * 好臻订单相关信息添加
     */
    private void hzOrderInfoSet(QueryHighDealOrderDtlRequest request, QueryHighDealOrderDtlResponse response, QueryHighDealOrderDtlVo queryHighDealOrderDtlVo) {
        response.setActualNeedPayAmt(getActualPayAmt(queryHighDealOrderDtlVo.getNetAppAmt(), queryHighDealOrderDtlVo.getFee()));
        if (DisCodeEnum.HZ.getCode().equals(queryHighDealOrderDtlVo.getDisCode())) {
            QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
            param.setFundCodeList(Collections.singletonList(response.getFundCode()));
            param.setTxAcctNo(request.getTxAcctNo());
            param.setDisCodeList(Collections.singletonList(queryHighDealOrderDtlVo.getDisCode()));
            List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(param);
            if (!CollectionUtils.isEmpty(confirmBalanceBaseInfoList)) {
                response.setIsFistAckFlag(YesOrNoEnum.NO.getCode());
            } else {
                response.setIsFistAckFlag(YesOrNoEnum.YES.getCode());
            }
            // 是否是修改订单
            HzOriginalOrderHisRecordPo hzOriginalOrderHisRecordPo = hzOriginalOrderHisRecordRepository.selectByNewDealNo(request.getDealNo());
            if (hzOriginalOrderHisRecordPo != null) {
                // 是否是修改订单
                response.setIsUpdateOrder(YesOrNoEnum.YES.getCode());
                // 本次付款是否低于上次付款成功的金额
                BigDecimal originalPayedAmt = hzOriginalOrderHisRecordPo.getOriginalPayedAmt();
                response.setOriginalPayedAmt(originalPayedAmt);
                if (originalPayedAmt != null && response.getActualNeedPayAmt() != null) {
                    response.setLessPreviousAmt(response.getActualNeedPayAmt().compareTo(originalPayedAmt) >= 0 ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
                } else {
                    response.setLessPreviousAmt(YesOrNoEnum.NO.getCode());
                }

            } else {
                response.setIsUpdateOrder(YesOrNoEnum.NO.getCode());
                response.setLessPreviousAmt(YesOrNoEnum.NO.getCode());
            }
        }
    }


    /**
     * 实际支付金额
     *
     * @param appAmt 净申请金额
     * @param fee    手续费
     * @return 实际支付金额
     */
    private BigDecimal getActualPayAmt(BigDecimal appAmt, BigDecimal fee) {
        if (fee == null) {
            return appAmt;
        }
        if (appAmt != null) {
            return appAmt.add(fee);
        } else {
            return null;
        }
    }

    /**
     * 子订单信息赋值
     *
     * @param request                 请求
     * @param response                返回结果
     * @param queryHighDealOrderDtlVo 订单明细
     */
    private void buildSubDealInfo(QueryHighDealOrderDtlRequest request, QueryHighDealOrderDtlResponse response, QueryHighDealOrderDtlVo queryHighDealOrderDtlVo) {
        List<String> dealNos = new ArrayList<>();
        response.setDisCode(queryHighDealOrderDtlVo.getDisCode());
        // 合并上报订单查询所有子订单
        if (YesOrNoEnum.YES.getCode().equals(queryHighDealOrderDtlVo.getMergeSubmitFlag())) {
            response.setMergeSubmitFlag(queryHighDealOrderDtlVo.getMergeSubmitFlag());

            List<QueryHighDealOrderDtlVo> orderList = highDealOrderDtlRepository.selectMergeSubmitOrderDtlByMainDealNo(queryHighDealOrderDtlVo.getMainDealOrderNo());
            // 构建主订单
            queryHighDealOrderDtlVo = getMergeMainOrder(orderList);
            // 构建子订单列表
            List<SubOrderBean> subOrderList = getSubOrderList(orderList, request);
            // 返回子订单列表
            response.setSubOrderList(subOrderList);
            // 查询是否有拆单明细
            dealNos = subOrderList.stream().map(SubOrderBean::getDealNo).collect(Collectors.toList());
        } else {
            // 查询是否有拆单明细
            dealNos.add(queryHighDealOrderDtlVo.getDealNo());
        }
        // 查询是否有拆单明细(合并赎回不返回拆分明细)
        if (CollectionUtil.isNotEmpty(dealNos)) {
            List<HighRedeemSplitDtlPo> splitDtlList = highRedeemSplitDtlRepository.selectByDealNo(dealNos);
            List<HighRedeemSplitDtlBean> beans = new ArrayList<>();
            if (!CollectionUtils.isEmpty(splitDtlList)) {
                // 分期成立，且不是巨额顺延，且只有一笔拆分明细（一期的份额满足），那么不返回给前端拆分明细
                if (!(YesOrNoEnum.YES.getCode().equals(queryHighDealOrderDtlVo.getStageFlag()) &&
                        !YesOrNoEnum.YES.getCode().equals(queryHighDealOrderDtlVo.getContinuanceFlag()) &&
                        splitDtlList.size() == dealNos.size())) {
                    HighRedeemSplitDtlBean bean = null;
                    for (HighRedeemSplitDtlPo po : splitDtlList) {
                        bean = new HighRedeemSplitDtlBean();
                        BeanUtils.copyProperties(po, bean);
                        QueryCustBankCardResult result = getBankCardInfo(po.getCpAcctNo(), request);
                        if (result != null) {
                            bean.setBankAcct(result.getBankAcct());
                            bean.setBankCode(result.getBankCode());
                            bean.setBankName(result.getBankName());
                        }
                        bean.setTaTradeDt(queryHighDealOrderDtlVo.getTaTradeDt());
                        bean.setSubmitTaDt(queryHighDealOrderDtlVo.getSubmitTaDt());
                        beans.add(bean);
                    }
                } else {
                    response.setSubFundCode(splitDtlList.get(0).getSubFundCode());
                    response.setJoinDt(splitDtlList.get(0).getJoinDt());
                }
            }
            response.setSplitDtlList(beans);
            // 数据转换
            BeanUtils.copyProperties(queryHighDealOrderDtlVo, response);
        }
    }

    /**
     * 添加申请时间
     */
    private void setAppTime(QueryHighDealOrderDtlVo order) {
        if (StringUtils.isBlank(order.getAppDt())) {
            order.setAppDt(order.getTaTradeDt());
        }
        if (StringUtils.isBlank(order.getAppTm())) {
            order.setAppTm("000000");
        }
        if (order.getAppDt() != null) {
            order.setAppDtm(DateUtils.formatToDate(order.getAppDt() + order.getAppTm(), DateUtils.YYYYMMDDHHMMSS));
        }
    }


    private void setProductSubType(QueryHighDealOrderDtlResponse response) {
        // 1.获取所有的产品code
        Map<String, HighProductDBInfoBean> highProductDBInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(Collections.singletonList(response.getFundCode()));
        HighProductDBInfoBean highProductDBInfoBean = highProductDBInfoMap.get(response.getFundCode());
        if (highProductDBInfoBean != null) {
            response.setProductSubType(highProductDBInfoBean.getFundSubType());
            response.setPeDivideCallFlag(highProductDBInfoBean.getPeDivideCallFlag());
        }
    }

    /**
     * 设置是否支付储蓄罐预约支付
     *
     * @param orderDtl
     * @param response
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/3/12 17:31
     * @since JDK 1.8
     */
    private void setSuportPiggyAppointPay(QueryHighDealOrderDtlVo orderDtl, QueryHighDealOrderDtlResponse response) {
        if (StringUtils.isNotEmpty(orderDtl.getTxPmtFlag()) && StringUtils.equalsAny(orderDtl.getTxPmtFlag(),
                TxPmtFlagEnum.PIGGY_FRZ_SUCC.getKey(),
                TxPmtFlagEnum.PIGGY_FRZ_FAIL.getKey(),
                TxPmtFlagEnum.PIGGY_UN_FRZ.getKey(),
                TxPmtFlagEnum.PIGGY_FRZING.getKey(),
                TxPmtFlagEnum.PIGGY_FRZ_PAY_FAIL.getKey(),
                TxPmtFlagEnum.PIGGY_FRZ_PAY_SUCC.getKey(),
                TxPmtFlagEnum.PIGGY_FRZ_PAYING.getKey(),
                TxPmtFlagEnum.PIGGY_UN_FRZ_FAIL.getKey(),
                TxPmtFlagEnum.PIGGY_UN_FRZ_SUCC.getKey(),
                TxPmtFlagEnum.PIGGY_UN_FRZING.getKey()
        )) {
            response.setSuportPiggyAppointPay(YesOrNoEnum.YES.getCode());
        } else {
            response.setSuportPiggyAppointPay(YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 处理历史迁移数据的多卡交易信息
     *
     * @param
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/3/12 17:26
     * @since JDK 1.8
     */
    private void processMultiCardInfo(QueryHighDealOrderDtlVo orderDtl, QueryHighDealOrderDtlRequest request, QueryHighDealOrderDtlResponse response) {
        if (!StringUtils.isEmpty(orderDtl.getMemo()) && orderDtl.getMemo().startsWith("MP|")) {
            Map<String, QueryCustBankCardResult> bankInfoMap = new HashMap<>();
            processMutiCard(bankInfoMap, orderDtl.getMemo());
            if (!bankInfoMap.isEmpty()) {
                final CountDownLatch bankInfoLatch = new CountDownLatch(bankInfoMap.size());
                for (QueryCustBankCardResult queryCustBankCardResult : bankInfoMap.values()) {
                    CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, bankInfoLatch, request.getTxAcctNo(), request.getDisCode(),
                            queryCustBankCardResult.getCpAcctNo(), queryCustBankCardResult, request.getOutletCode()));
                }

                try {
                    bankInfoLatch.await();
                } catch (InterruptedException e) {
                    logger.error("QueryDealOrderListFacadeService|latch.await exception.", e);
                    Thread.currentThread().interrupt();
                }
            }

            response.setMemo(buildMemo(bankInfoMap, orderDtl.getMemo()));
        }
    }

    /**
     * 查询银行卡信息
     *
     * @param cpAcctNo
     * @param request
     * @return com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult
     * @author: huaqiang.liu
     * @date: 2021/3/12 17:20
     * @since JDK 1.8
     */
    private QueryCustBankCardResult getBankCardInfo(String cpAcctNo, QueryHighDealOrderDtlRequest request) {
        if (!StringUtils.isEmpty(cpAcctNo)) {
            QueryCustBankCardContext ctx = new QueryCustBankCardContext();
            ctx.setCpAcctNo(cpAcctNo);
            ctx.setDisCode(request.getDisCode());
            ctx.setTxAcctNo(request.getTxAcctNo());
            ctx.setOutletCode(request.getOutletCode());
            return queryCustBankCardOuterService.queryCudtBankCard(ctx);
        } else {
            return null;
        }
    }

    /**
     * 获取合并上报主订单
     *
     * @param orderList
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/3/12 16:59
     * @since JDK 1.8
     */
    private QueryHighDealOrderDtlVo getMergeMainOrder(List<QueryHighDealOrderDtlVo> orderList) {
        QueryHighDealOrderDtlVo mainOrder = new QueryHighDealOrderDtlVo();
        // 创建副本，不直接修改
        BeanUtils.copyProperties(orderList.get(0), mainOrder);

        List<String> orderStatList = new ArrayList<>(orderList.size());
        orderStatList.add(mainOrder.getOrderStatus());
        List<String> payStatList = new ArrayList<>(orderList.size());
        payStatList.add(mainOrder.getPayStatus());

        for (int i = 1; i < orderList.size(); i++) {
            QueryHighDealOrderDtlVo order = orderList.get(i);
            // 预申请份额
            mainOrder.setPreAppVol(MergeSubmitUtils.add(mainOrder.getPreAppVol(), order.getPreAppVol()));
            // 申请金额
            mainOrder.setAppAmt(MergeSubmitUtils.add(mainOrder.getAppAmt(), order.getAppAmt()));
            // 申请份额
            mainOrder.setAppVol(MergeSubmitUtils.add(mainOrder.getAppVol(), order.getAppVol()));
            // 费
            mainOrder.setFee(MergeSubmitUtils.add(mainOrder.getFee(), order.getFee()));
            // 确认金额
            mainOrder.setAckAmt(MergeSubmitUtils.add(mainOrder.getAckAmt(), order.getAckAmt()));
            // 确认份额
            mainOrder.setAckVol(MergeSubmitUtils.add(mainOrder.getAckVol(), order.getAckVol()));
            // 预估手续费
            mainOrder.setEsitmateFee(MergeSubmitUtils.add(mainOrder.getEsitmateFee(), order.getEsitmateFee()));
            // 业绩报酬
            mainOrder.setAchievementPay(MergeSubmitUtils.add(mainOrder.getAchievementPay(), order.getAchievementPay()));
            // 订单状态
            orderStatList.add(order.getOrderStatus());
            // 支付状态
            payStatList.add(order.getPayStatus());
        }

        // 转换合并订单状态
        mainOrder.setOrderStatus(MergeSubmitUtils.convertMergeOrderStatus(orderStatList));
        mainOrder.setPayStatus(MergeSubmitUtils.convertMergePayStatus(payStatList));

        return mainOrder;
    }

    /**
     * 获取子订单列表
     *
     * @param orderList
     * @return java.util.List<com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.bean.SubOrderBean>
     * @author: huaqiang.liu
     * @date: 2021/3/12 17:05
     * @since JDK 1.8
     */
    private List<SubOrderBean> getSubOrderList(List<QueryHighDealOrderDtlVo> orderList, QueryHighDealOrderDtlRequest request) {
        List<SubOrderBean> subOrderList = new ArrayList<>(orderList.size());
        orderList.forEach(order -> {
            SubOrderBean subOrder = new SubOrderBean();
            subOrder.setDealNo(order.getDealNo());
            subOrder.setPreAppVol(order.getPreAppVol());
            subOrder.setAppVol(order.getAppVol());
            subOrder.setAppAmt(order.getAppAmt());
            subOrder.setFee(order.getFee());
            subOrder.setAckAmt(order.getAckAmt());
            subOrder.setAckVol(order.getAckVol());
            subOrder.setPaymentType(order.getPaymentType());
            subOrder.setCpAcctNo(order.getCpAcctNo());
            // 查询银行卡信息
            if (!BusinessCodeEnum.DIV_MODE_CHANGE.getMCode().equals(order.getmBusiCode())) {
                QueryCustBankCardResult result = getBankCardInfo(order.getCpAcctNo(), request);
                if (result != null) {
                    subOrder.setBankAcct(result.getBankAcctMask());
                    subOrder.setBankCode(result.getBankCode());
                    subOrder.setBankName(result.getBankName());
                }
            }
            subOrderList.add(subOrder);
        });
        return subOrderList;
    }

    /**
     * processAdvanceOrder:(处理递延订单)
     *
     * @param queryHighDealOrderDtl
     * <AUTHOR>
     * @date 2017年11月29日 上午11:46:38
     */
    private void processAdvanceOrder(QueryHighDealOrderDtlResponse queryHighDealOrderDtl) {
        if (queryHighDealOrderDtl == null) {
            return;
        }

        if (AdvanceFlagEnum.TRADE_ADVENCE.getCode().equals(queryHighDealOrderDtl.getAdvanceFlag())) {
            if (OrderStatusEnum.FORCE_CANCELED.getCode().equals(queryHighDealOrderDtl.getOrderStatus())
                    && PayStatusEnum.PAY_FAIL.getCode().equals(queryHighDealOrderDtl.getPayStatus())) {
                queryHighDealOrderDtl.setOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode());
                queryHighDealOrderDtl.setPayStatus(PayStatusEnum.PAYING.getCode());
            }
        }
    }

    /**
     * getHighConfirmAndPaymentReceiptDt:(查询基金预计确认日期)
     *
     * @param fundCode        产品代码
     * @param busiCode        业务码  020-认购；022-申购；024-赎回；036-基金转换；039-定时定额投资；029-修改分红方式
     * @param businessType    业务类型 :1-确认日期 2-预计到账日期 3-预计打款日期 4-赎回到储蓄罐份额可用日期 其余-查全部
     * @param redeemDirection 赎回去向 0-银行卡 1-储蓄罐 默认银行卡
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:25:58
     */
    public HighConfirmAndPaymentReceiptDtBean getHighConfirmAndPaymentReceiptDt(String fundCode, String busiCode, String businessType, String redeemDirection) {

        List<HighConfirmAndPaymentReceiptParamContext> paramContextList = new ArrayList<HighConfirmAndPaymentReceiptParamContext>();
        HighConfirmAndPaymentReceiptParamContext paramContext = new HighConfirmAndPaymentReceiptParamContext();
        paramContext.setFundCode(fundCode);
        paramContext.setBusiCode(busiCode);
        paramContext.setBusinessType(businessType);

        paramContextList.add(paramContext);

        List<HighConfirmAndPaymentReceiptDtBean> qryRstList = queryHighProductOuterService.getHighConfirmAndPaymentReceiptDt(paramContextList);

        if (CollectionUtils.isEmpty(qryRstList)) {
            return null;
        }

        HighConfirmAndPaymentReceiptDtBean highConfirmAndPaymentReceiptDtBean = qryRstList.get(0);

        return highConfirmAndPaymentReceiptDtBean;

    }

    /**
     * @param orderDtl
     * @return com.howbuy.tms.common.enums.busi.CanCancelFlagEnum
     * @Description 计算撤单标示
     * <AUTHOR>
     * @Date 2020/7/6 17:42
     **/
    private CanCancelFlagEnum calCancelFlag(QueryHighDealOrderDtlVo orderDtl) {
        Date currDate = new Date();
        // 非申请成功订单不支持撤单
        if (!OrderStatusEnum.APP_SUCCESS.getCode().equals(orderDtl.getOrderStatus())) {
            logger.info("calCancelFlag|dealNo:{}, mBusiCode:{}, orderStatus：{} is not 1-appSucc", orderDtl.getDealNo(), orderDtl.getmBusiCode(), orderDtl.getOrderStatus());
            return CanCancelFlagEnum.CAN_NOT;
        }
        // 上报日15点后不能撤单
        Date submitEndTime = DateUtils.formatToDate(orderDtl.getSubmitTaDt() + "150000", DateUtils.YYYYMMDDHHMMSS);
        if (submitEndTime.compareTo(currDate) <= 0) {
            logger.info("calCancelFlag|dealNo:{},  mBusiCode:{},currDate：{} is over submitEndDtm:{}", orderDtl.getDealNo(), orderDtl.getmBusiCode(), currDate, submitEndTime);
            return CanCancelFlagEnum.CAN_NOT;
        }
        if (BusinessCodeEnum.PURCHASE.getMCode().equals(orderDtl.getmBusiCode())
                || BusinessCodeEnum.SUBS.getMCode().equals(orderDtl.getmBusiCode())) {
            // 认申购冷静期后不能撤单
            if (orderDtl.getCalmDtm() != null && orderDtl.getCalmDtm().compareTo(currDate) <= 0) {
                logger.info("calCancelFlag|dealNo:{},  mBusiCode:{}, currDate：{} is over calDtm:{}", orderDtl.getDealNo(), orderDtl.getmBusiCode(), currDate, orderDtl.getCalmDtm());
                return CanCancelFlagEnum.CAN_NOT;
            }
        } else if (BusinessCodeEnum.REDEEM.getMCode().equals(orderDtl.getmBusiCode())) {
            // 撤单截止日期默认上报Ta日
            String cancelEndDt = orderDtl.getSubmitTaDt();
            HighProductInfoBean productInfo = queryHighProductOuterService.getHighProductInfo(orderDtl.getFundCode());
            if (productInfo == null) {
                logger.info("calCancelFlag|dealNo:{},  mBusiCode:{}, fundCode：{} productinfo is null", orderDtl.getDealNo(), orderDtl.getmBusiCode(), orderDtl.getFundCode());
                return CanCancelFlagEnum.CAN_NOT;
            }
            if (IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(productInfo.getIsScheduledTrade())
                    || IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(productInfo.getIsScheduledTrade())) {
                // 支持预约赎回的订单，取预约日历的预约截止日作为撤单截止日
                ProductAppointmentInfoBean appointmentInfo =
                        queryHighProductOuterService.queryAppointmentInfoByAppointDate(orderDtl.getFundCode(), "1", orderDtl.getFundShareClass(), orderDtl.getDisCode(), orderDtl.getAppDtm());
                if (appointmentInfo == null) {
                    logger.info("calCancelFlag|dealNo:{},  mBusiCode:{}, fundCode：{}, appDtm:{},  appointmentInfo is null", orderDtl.getDealNo(), orderDtl.getmBusiCode(), orderDtl.getFundCode(), orderDtl.getAppDtm());
                    return CanCancelFlagEnum.CAN_NOT;
                }
                String apponitEndDt = appointmentInfo.getApponitEndDt();
                if (!StringUtils.isEmpty(apponitEndDt)) {
                    cancelEndDt = apponitEndDt;
                }
            }
            // 仅撤单截止日15点前可撤单
            String cancelEndDtm = cancelEndDt + "150000";
            if (currDate.compareTo(DateUtil.formatToDate(cancelEndDtm, DateUtil.YYYYMMDDHHMMSS)) < 0) {
                return CanCancelFlagEnum.CAN;
            } else {
                logger.info("calCancelFlag|dealNo:{},  mBusiCode:{}, fundCode:{}, currDtm:{} over cancelEndDtm:{}", orderDtl.getDealNo(), orderDtl.getmBusiCode(), orderDtl.getFundCode(), currDate, cancelEndDtm);
                return CanCancelFlagEnum.CAN_NOT;
            }
        } else {
            // 其他业务不支持撤单
            return CanCancelFlagEnum.CAN_NOT;
        }
        return CanCancelFlagEnum.CAN;
    }

}