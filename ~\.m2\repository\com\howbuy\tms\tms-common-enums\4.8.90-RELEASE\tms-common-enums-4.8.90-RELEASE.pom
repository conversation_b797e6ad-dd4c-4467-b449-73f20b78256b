<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.tms</groupId>
	<artifactId>tms-common-enums</artifactId>
	<name>tms-common-enums</name>
	<version>4.8.90-RELEASE</version>
	<packaging>jar</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<java.version>1.6</java.version>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
</project>