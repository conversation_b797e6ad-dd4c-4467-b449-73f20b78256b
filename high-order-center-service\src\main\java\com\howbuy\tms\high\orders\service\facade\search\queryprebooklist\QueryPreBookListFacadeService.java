/**
 * Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.queryprebooklist;


import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.BusiCodeEnum;
import com.howbuy.interlayer.product.model.HighProductControlModel;
import com.howbuy.interlayer.product.model.HighProductDBInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryTxAcctByHboneResult;
import com.howbuy.tms.common.outerservice.crm.td.queryprebooklist.QueryPreBookListOuterContext;
import com.howbuy.tms.common.outerservice.crm.td.queryprebooklist.QueryPreBookListOuterResult;
import com.howbuy.tms.common.outerservice.crm.td.queryprebooklist.QueryPreBookListOuterService;
import com.howbuy.tms.common.outerservice.crm.td.queryprebooklist.bean.PreBookBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.context.QueryHighActiDiscountContext;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListRequest;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse.PreBookListBean;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.business.task.*;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.repository.SubscribeAmtDetailRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(查询预约列表)
 * @reason:
 * @date 2018年1月5日 上午9:44:05
 * @since JDK 1.6
 */
@DubboService
@Service("queryPreBookListFacade")
public class QueryPreBookListFacadeService extends AbstractBusiProcess implements QueryPreBookListFacade {
    private static final Logger logger = LogManager.getLogger(QueryPreBookListFacadeService.class);

    @Autowired
    private QueryPreBookListOuterService queryPreBookListOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryAccHboneInfoOuterService queryAccHboneInfoOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade.execute(QueryPreBookListRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryPreBookListFacadeService
     * @apiName execute
     * @apiDescription 查询预约列表
     * @apiParam (请求参数) {Array} preType 预约类型 1-纸质成单； 2-电子成单； 3-无纸化交易
     * @apiParam (请求参数) {Array} tradeType 交易类型 1-购买 2-追加 3-赎回
     * @apiParam (请求参数) {Array} preBookState 预约单状态:1-未确认；2-已确认；4-已撤销
     * @apiParam (请求参数) {String} custName 客户姓名
     * @apiParam (请求参数) {String} idNo 证件号
     * @apiParam (请求参数) {String} fundCode 产品代码
     * @apiParam (请求参数) {String} fundName 产品名称
     * @apiParam (请求参数) {String} preId 预约号
     * @apiParam (请求参数) {String} startDt 开始日期
     * @apiParam (请求参数) {String} endDt 结束日期
     * @apiParam (请求参数) {String} useFlag 使用状态  1-未使用 2-已使用
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=YgyRIF21Qu&preId=AFp7&preType=pWdy0&preBookState=TtehRtbsd&endDt=E0uUR7sTqS&pageSize=6025&disCode=yX&txChannel=6fnjC&idNo=mySI&fundCode=BOR6RO4&pageNo=7339&operIp=Uy&txAcctNo=B4RO4Y1eLt&appDt=9E0k&notFilterHkFund=mCg&tradeType=goAz5Cx&hbOneNo=5iUgVxOK&custName=47&appTm=mj&useFlag=q0wcJJ&subOutletCode=Qy0&startDt=7HOi2vtiQs&dataTrack=5MpS&fundName=fUM5&txCode=cOnOlzCf&outletCode=1IPcCuwL
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {Array} preBookList
     * @apiSuccess (响应结果) {String} preBookList.preId 预约单标识，CRM唯一
     * @apiSuccess (响应结果) {String} preBookList.hboneNo 一帐通号
     * @apiSuccess (响应结果) {String} preBookList.feeCalMode 费用计算方式,0-外扣法；1-内扣法
     * @apiSuccess (响应结果) {String} preBookList.custNo 客户号
     * @apiSuccess (响应结果) {String} preBookList.custName 姓名
     * @apiSuccess (响应结果) {String} preBookList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} preBookList.fundName 产品名称
     * @apiSuccess (响应结果) {String} preBookList.fundType 产品类别
     * @apiSuccess (响应结果) {String} preBookList.tradeType 交易类型 1-购买 2-追加 3-赎回
     * @apiSuccess (响应结果) {String} preBookList.idType 证件类型
     * @apiSuccess (响应结果) {String} preBookList.idNo 证件号
     * @apiSuccess (响应结果) {String} preBookList.prebookState 预约单状态:1-未确认；2-已确认；4-已撤销
     * @apiSuccess (响应结果) {String} preBookList.nopaperState 无纸化预约单状态 1-未确认； 2-已确认； 3-驳回
     * @apiSuccess (响应结果) {Number} preBookList.ackAmt 预约金额
     * @apiSuccess (响应结果) {Number} preBookList.sellVol 预约份额
     * @apiSuccess (响应结果) {Number} preBookList.fee 手续费
     * @apiSuccess (响应结果) {Number} preBookList.feeRate 费率
     * @apiSuccess (响应结果) {Number} preBookList.discount 预约折扣
     * @apiSuccess (响应结果) {Number} preBookList.disCountFee 无折扣手续费
     * @apiSuccess (响应结果) {String} preBookList.activityDiscountEndDate 活动折扣截止日
     * @apiSuccess (响应结果) {String} preBookList.preType 预约类型 1：纸质成单； 2：电子成单； 3：无纸化；
     * @apiSuccess (响应结果) {String} preBookList.openStartDt 开放开始日
     * @apiSuccess (响应结果) {String} preBookList.openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} preBookList.payEndDate 截止打款时间
     * @apiSuccess (响应结果) {String} preBookList.supportAdvanceFlag 支持提前下单 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} preBookList.investType 客户投资者类型 0-机构 1-个人
     * @apiSuccess (响应结果) {String} preBookList.creDt 预约日期
     * @apiSuccess (响应结果) {String} preBookList.feeRateMethod 手续费费率方式
     * @apiSuccess (响应结果) {String} preBookList.mBusiCode 中台业务码
     * @apiSuccess (响应结果) {String} preBookList.orderId 中台订单号
     * @apiSuccess (响应结果) {String} preBookList.bankAcctNo 中台订单号
     * @apiSuccess (响应结果) {String} preBookList.shareClass 份额类型 A-前收费 B-后收费
     * @apiSuccess (响应结果) {String} preBookList.redeemDate 赎回开放日
     * @apiSuccess (响应结果) {String} preBookList.doubleNeedFlag
     * @apiSuccess (响应结果) {String} preBookList.doubleHandleFlag
     * @apiSuccess (响应结果) {Number} preBookList.doubleHandleDt
     * @apiSuccess (响应结果) {String} preBookList.firstPreId
     * @apiSuccess (响应结果) {Number} preBookList.appointSubsAmt 预约认缴金额
     * @apiSuccess (响应结果) {Number} preBookList.subsAmt 认缴金额
     * @apiSuccess (响应结果) {String} preBookList.productSource 产品信息来源 SMOP，DB（仅SMOP未配置时，取DB）
     * @apiSuccess (响应结果) {String} preBookList.currency 币种
     * @apiSuccess (响应结果) {String} preBookList.disCode 分销渠道
     * @apiSuccess (响应结果) {String} preBookList.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"xSFgv7","hasHZProduct":"W","totalPage":8509,"pageNo":3537,"description":"4CkZYPEbTH","hasHKProduct":"FEe","totalCount":5355,"preBookList":[{"activityDiscountEndDate":"0Eyh","preId":"Z16j","productSource":"FLeghe","payEndDate":"tBagmzO","preType":"GAepGi","bankAcctNo":"TKg9rNuBn","disCountFee":5536.************,"feeRateMethod":"tx6","orderId":"USNh8lo","shareClass":"x0","fee":3124.*************,"openStartDt":"siw0AuW4Ms","discount":2184.*************,"sellVol":6564.************,"doubleNeedFlag":"w3KW8Kl9","disCode":"gHhX16MD","idNo":"XWNF9W20g","feeRate":5516.************,"mBusiCode":"O","fundType":"y0W","nopaperState":"Y4CtPuOk","redeemDate":"YOjZbvmQJ","fundCode":"fNzEYS","ackAmt":1810.***********,"supportAdvanceFlag":"Z","firstPreId":"g7Giquq","currency":"bI","openEndDt":"Ct","feeCalMode":"w","tradeType":"OxbJ3UeU","hboneNo":"szRo","investType":"bbohf","appointSubsAmt":2518.*************,"custNo":"0y","idType":"gV","peDivideCallFlag":"yvCrx3em","custName":"e8FXq2","doubleHandleDt":*************,"creDt":"MSkAqPS","prebookState":"MIt","doubleHandleFlag":"rqotqau","subsAmt":4259.***********,"fundName":"xfP"}]}
     */
    @Override
    public QueryPreBookListResponse execute(QueryPreBookListRequest request) {
        logger.info("QueryPreBookListFacadeService|execute|start");
        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }
        QueryPreBookListResponse resp = new QueryPreBookListResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        resp.setHasHZProduct(YesOrNoEnum.NO.getCode());
        resp.setHasHKProduct(YesOrNoEnum.NO.getCode());
        if (StringUtils.isNotEmpty(request.getTxAcctNo())) {
            String hboneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(request.getTxAcctNo());
            request.setHbOneNo(hboneNo);
        }
        //创建预约列表查询条件
        QueryPreBookListOuterContext queryContext = createQueryContext(request);
        QueryPreBookListOuterResult queryPreBookListOuterResult = queryPreBookListOuterService.queryPreBookList(queryContext);

        if (queryPreBookListOuterResult == null || CollectionUtils.isEmpty(queryPreBookListOuterResult.getPreBookBeanList())) {
            logger.info("QueryPreBookListFacadeService|queryPreBookListOuterService|preBookList is null");
            return resp;
        }
        resp.setPageNo(queryPreBookListOuterResult.getPageNo());
        resp.setTotalPage(queryPreBookListOuterResult.getTotalPage());
        resp.setTotalCount(queryPreBookListOuterResult.getTotalCount());
        List<PreBookBean> preBookList = queryPreBookListOuterResult.getPreBookBeanList();
        //处理预约信息
        procPreBookList(resp, preBookList, request);

        return resp;

    }

    /**
     * procPreBookList:(预约列表处理)
     *
     * @param resp
     * @param preBookList
     * <AUTHOR>
     * @date 2018年1月9日 上午10:01:45
     */
    private void procPreBookList(QueryPreBookListResponse resp, List<PreBookBean> preBookList, QueryPreBookListRequest request) {

        //产品基本信息map
        Map<String, HighProductBaseInfoBean> productMap = new HashMap<>();
        //产品状态信息map
        Map<String, HighProductStatInfoBean> highProductStatInfoMap = new HashMap<>();

        //产品信息费率map
        Map<String, HighProductFeeRateBean> highProductFeeRateMap = new HashMap<>();

        //一帐通号与交易账号map
        Map<String, QueryTxAcctByHboneResult> hboneNoAndTxAcctNoMap = new HashMap<>();

        //一帐通信息Map
        Map<String, QueryAccHboneInfoResult> hbOneNoInfoMap = new HashMap<>();

        //活动折扣map
        Map<String, HighProductActiDiscountListBean> highProductActiDiscountListMap = new HashMap<>();
        // 修改预约单信息
        setPreBookInfo(preBookList, productMap, highProductStatInfoMap, hboneNoAndTxAcctNoMap, hbOneNoInfoMap);

        //查询产品基本信息map
        queryProductBaseInfoMap(productMap);
        // SMOP没有产品信息时，查询DB产品信息
        Map<String, HighProductInfoBean> dbFundInfoMap = queryDBFundInfoMap(productMap);

        List<String> fundCodeList = preBookList.stream().map(PreBookBean::getFundCode).distinct().collect(Collectors.toList());

        List<HighProductControlModel> highProductControlList = highProductService.getHighProductControlList(fundCodeList);
        Map<String, List<HighProductControlModel>> highProductControlModelMap = CollectionUtils.isEmpty(highProductControlList) ? new HashMap<>() : highProductControlList.stream().collect(Collectors.groupingBy(HighProductControlModel::getFundCode));
        // 查询产品香港信息
        List<HighProductDBInfoModel> highProductDbInfoList = highProductService.getHighProductDBInfo(fundCodeList);
        Map<String, String> hkMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(highProductDbInfoList)) {
            highProductDbInfoList.forEach(highProductDbInfoModel -> {
                hkMap.put(highProductDbInfoModel.getFundCode(), highProductDbInfoModel.getHkSaleFlag());
            });
        }

        //批量查询产品状态
        //查询当前工作日
        String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());
        //查询产品状态map
        queryProductStatMap(highProductStatInfoMap, taTradeDt);

        //查询一帐通号和客户号的对应map
        queryHboneNoAndTxAcctNoMap(hboneNoAndTxAcctNoMap, request);

        //查询一帐通信息
        queryHbOneNoInfoMap(hbOneNoInfoMap);

        //购买预约信息
        Map<String, ProductAppointmentInfoBean> buyProductAppointmentMap = new HashMap<String, ProductAppointmentInfoBean>();

        //赎回预约信息map
        Map<String, ProductAppointmentInfoBean> sellProductAppointmentMap = new HashMap<String, ProductAppointmentInfoBean>();

        List<PreBookListBean> rstList = setAndFilterPreBook(resp, preBookList, request, productMap, highProductStatInfoMap, highProductFeeRateMap, hboneNoAndTxAcctNoMap, hbOneNoInfoMap, highProductActiDiscountListMap, dbFundInfoMap, highProductControlModelMap, hkMap, buyProductAppointmentMap, sellProductAppointmentMap);

        String dateStr = request.getAppDt() + request.getAppTm();
        Date appDtm = DateUtils.formatToDate(dateStr, DateUtils.YYYYMMDDHHMMSS);

        //查询购买预约开放日
        queryProductAppointmentMap(buyProductAppointmentMap, "0", appDtm);

        //查赎回预约开放日
        queryProductAppointmentMap(sellProductAppointmentMap, "1", appDtm);

        //构建预约信息
        buildSupplyPreBookInfo(rstList, buyProductAppointmentMap, sellProductAppointmentMap);

        //查询费率map
        queryHighProductFeeRateMap(highProductFeeRateMap, rstList);

        //查询活动折扣map
        querHighProductActiDiscountMap(highProductActiDiscountListMap);

        //构建费率信息,活动折扣
        buildPreBookInfoFee(rstList, highProductFeeRateMap, highProductActiDiscountListMap);


        resp.setPreBookList(rstList);
    }

    /**
     * 过滤设置预约信息
     */
    private List<PreBookListBean> setAndFilterPreBook(QueryPreBookListResponse resp, List<PreBookBean> preBookList, QueryPreBookListRequest request, Map<String, HighProductBaseInfoBean> productMap, Map<String, HighProductStatInfoBean> highProductStatInfoMap, Map<String, HighProductFeeRateBean> highProductFeeRateMap, Map<String, QueryTxAcctByHboneResult> hboneNoAndTxAcctNoMap, Map<String, QueryAccHboneInfoResult> hbOneNoInfoMap, Map<String, HighProductActiDiscountListBean> highProductActiDiscountListMap, Map<String, HighProductInfoBean> dbFundInfoMap, Map<String, List<HighProductControlModel>> highProductControlModelMap, Map<String, String> hkMap, Map<String, ProductAppointmentInfoBean> buyProductAppointmentMap, Map<String, ProductAppointmentInfoBean> sellProductAppointmentMap) {
        List<PreBookListBean> rstList = new ArrayList<>();
        for (PreBookBean preBookBean : preBookList) {
            String productSource = "SMOP";
            HighProductBaseInfoBean highProductBaseBean = productMap.get(preBookBean.getFundCode());
            if (highProductBaseBean == null || StringUtils.isEmpty(highProductBaseBean.getFundAttr())) {
                logger.info("QueryPreBookListFacadeService|prebook productCode:{} is not op supported", preBookBean.getFundCode());
                // SMOP无产品信息，取DB产品信息
                highProductBaseBean = dbFundInfoMap.get(preBookBean.getFundCode());
                productSource = "DB";
                if (highProductBaseBean == null) {
                    logger.info("QueryPreBookListFacadeService|prebook productCode:{} is not db supported", preBookBean.getFundCode());
                    continue;
                }
            }
            // 如果是非授权用户,就需要过滤好臻,香港产品
            List<HighProductControlModel> highProductControlModels = highProductControlModelMap.get(preBookBean.getFundCode());
            if (CollectionUtils.isEmpty(highProductControlModels)) {
                logger.info("没有产品控制信息,不展示该产品,fundCode={}", preBookBean.getFundCode());
                continue;
            }
            boolean needFilter = false;
            // 过滤好臻产品
            if (!CollectionUtils.isEmpty(highProductControlModels) && DisCodeEnum.HZ.getCode().equals(highProductControlModels.get(0).getDisCode())) {
                resp.setHasHZProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
                    needFilter = true;
                }
            }
            // 过滤香港产品
            if (StringUtils.isNotBlank(hkMap.get(preBookBean.getFundCode())) && YesOrNoEnum.YES.getCode().equals(hkMap.get(preBookBean.getFundCode()))) {
                resp.setHasHKProduct(YesOrNoEnum.YES.getCode());
                needFilter = true;
            }
            if (needFilter) {
                continue;
            }

            QueryAccHboneInfoResult queryAccHboneInfoResult = hbOneNoInfoMap.get(preBookBean.getHboneNo());
            QueryTxAcctByHboneResult queryTxAcctByHboneResult = hboneNoAndTxAcctNoMap.get(preBookBean.getHboneNo());

            //构建预约列表返回对象
            PreBookListBean preBookListBean = new PreBookListBean();
            buildPreBookListBean(preBookBean, preBookListBean, queryAccHboneInfoResult, highProductBaseBean, queryTxAcctByHboneResult);
            preBookListBean.setDisCode(highProductControlModels.get(0).getDisCode());
            preBookListBean.setProductSource(productSource);
            preBookListBean.setDiscountUseType(preBookBean.getDiscountUseType());
            preBookListBean.setDiscountAmt(preBookBean.getDiscountAmt());
            preBookListBean.setPeDivideCallFlag(highProductBaseBean.getPeDivideCallFlag());
            preBookListBean.setFeeCalMode(highProductBaseBean.getFeeCalMode());
            //业务类型 0-购买 1-赎回
            String busiType = convertBusiType(preBookListBean.getTradeType());

            //购买预约
            if ("0".equals(busiType)) {
                //专户基金
                if (ProductTypeEnum.ZHUANHU.getCode().equals(highProductBaseBean.getFundType()) ||
                        ProductTypeEnum.QUANSHANG_XIAOJIHE.getCode().equals(highProductBaseBean.getFundType())) {
                    HighProductActiDiscountListBean highProductActiDiscountListBean = new HighProductActiDiscountListBean();
                    highProductActiDiscountListMap.put(highProductBaseBean.getFundCode(), highProductActiDiscountListBean);
                }
            }

            //构造预约信息map
            if ("1".equals(preBookListBean.getSupportAdvanceFlag())) {
                if ("0".equals(busiType)) {
                    buildProductAppointmentMap(buyProductAppointmentMap, highProductBaseBean, preBookListBean.getPreId());

                } else if ("1".equals(busiType)) {
                    buildProductAppointmentMap(sellProductAppointmentMap, highProductBaseBean, preBookListBean.getPreId());
                }

            }

            //构造费率map
            HighProductStatInfoBean highProductStatInfoBean = highProductStatInfoMap.get(highProductBaseBean.getFundCode());
            buildHighFeeRateMap(highProductFeeRateMap, highProductStatInfoBean, preBookListBean);

            rstList.add(preBookListBean);
        }
        return rstList;
    }

    private void setPreBookInfo(List<PreBookBean> preBookList, Map<String, HighProductBaseInfoBean> productMap, Map<String, HighProductStatInfoBean> highProductStatInfoMap, Map<String, QueryTxAcctByHboneResult> hboneNoAndTxAcctNoMap, Map<String, QueryAccHboneInfoResult> hbOneNoInfoMap) {
        for (PreBookBean preBookBean : preBookList) {
            //产品基本信息map
            HighProductBaseInfoBean highProductBaseBean = new HighProductBaseInfoBean();
            highProductBaseBean.setFundCode(preBookBean.getFundCode());
            productMap.put(preBookBean.getFundCode(), highProductBaseBean);

            //产品状态信息map
            HighProductStatInfoBean highProductStatInfoBean = new HighProductStatInfoBean();
            highProductStatInfoMap.put(preBookBean.getFundCode(), highProductStatInfoBean);

            //一帐通号和交易账号对应关系
            QueryTxAcctByHboneResult queryTxAcctByHboneResult = new QueryTxAcctByHboneResult();
            queryTxAcctByHboneResult.setHboneNo(preBookBean.getHboneNo());
            hboneNoAndTxAcctNoMap.put(preBookBean.getHboneNo(), queryTxAcctByHboneResult);

            //一帐通信息map
            QueryAccHboneInfoResult queryAccHboneInfoResult = new QueryAccHboneInfoResult();
            queryAccHboneInfoResult.setHboneNo(preBookBean.getHboneNo());
            hbOneNoInfoMap.put(preBookBean.getHboneNo(), queryAccHboneInfoResult);
        }
    }

    /**
     * SMOP未配置的查询DB产品信息
     *
     * @param productMap
     * @return java.util.Map<java.lang.String, com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean>
     * @author: huaqiang.liu
     * @date: 2021/1/28 15:35
     * @since JDK 1.8
     */
    private Map<String, HighProductInfoBean> queryDBFundInfoMap(Map<String, HighProductBaseInfoBean> productMap) {
        List<String> fundCodes = new ArrayList<>();
        // 找出smop未查询到的基金
        productMap.values().forEach(product -> {
            if (StringUtils.isBlank(product.getFundAttr())) {
                fundCodes.add(product.getFundCode());
            }
        });
        if (fundCodes.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        // 查询DB产品信息
        Map<String, HighProductDBInfoBean> dbMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodes);
        if (dbMap == null) {
            return Collections.EMPTY_MAP;
        } else {
            Map<String, HighProductInfoBean> productDBMap = new HashMap<>();
            dbMap.values().forEach(db -> {
                HighProductInfoBean product = new HighProductInfoBean();
                product.setFundCode(db.getFundCode());
                product.setFundAttr(db.getFundAttr());
                product.setFundType(db.getFundType());
                product.setHkSaleFlag(db.getHkSaleFlag());
                product.setPeDivideCallFlag(db.getPeDivideCallFlag());
                productDBMap.put(db.getFundCode(), product);
            });
            return productDBMap;
        }
    }

    /**
     * buildPreBookInfoFee:(构建手续费信息)
     *
     * <AUTHOR>
     * @date 2018年1月22日 下午3:43:45
     */
    public void buildPreBookInfoFee(List<PreBookListBean> rstList, Map<String, HighProductFeeRateBean> highProductFeeRateMap, Map<String, HighProductActiDiscountListBean> highProductActiDiscountListMap) {
        //计算打款截止日，预约开放日
        for (PreBookListBean preBookListBean : rstList) {
            //计算购买费率
            if (DisCodeEnum.HZ.getCode().equals(preBookListBean.getDisCode())) {
                hzFeeInfoSet(highProductFeeRateMap, highProductActiDiscountListMap, preBookListBean);
            } else {
                hmFeeInfoSet(highProductFeeRateMap, highProductActiDiscountListMap, preBookListBean);
            }
        }
    }

    private void hzFeeInfoSet(Map<String, HighProductFeeRateBean> highProductFeeRateMap, Map<String, HighProductActiDiscountListBean> highProductActiDiscountListMap, PreBookListBean preBookListBean) {
        if ("0".equals(convertBusiType(preBookListBean.getTradeType())) || "1".equals(convertBusiType(preBookListBean.getTradeType()))) {
            HighProductFeeRateBean highProductFeeRateBean = highProductFeeRateMap.get(preBookListBean.getPreId());
            BigDecimal appointAmt = getAppointAmt(highProductFeeRateBean, preBookListBean);
            if (highProductFeeRateBean != null) {
                preBookListBean.setFee(getFee(appointAmt, preBookListBean, highProductFeeRateBean));
                preBookListBean.setFeeRate(highProductFeeRateBean.getFeeRate());
                preBookListBean.setFeeRateMethod(highProductFeeRateBean.getGetFeeRateMethod());
                preBookListBean.setDisCountFee(getDisCountFee(appointAmt, preBookListBean, highProductFeeRateBean));
            }
            //活动截止日
            HighProductActiDiscountListBean highProductActiDiscountListBean = highProductActiDiscountListMap.get(preBookListBean.getFundCode());
            if (highProductActiDiscountListBean != null && "1".equals(highProductActiDiscountListBean.getActiFlag())) {
                if (!CollectionUtils.isEmpty(highProductActiDiscountListBean.getDiscountList())) {
                    String activityDiscountEndDate = highProductActiDiscountListBean.getDiscountList().get(0).getEndDt() + highProductActiDiscountListBean.getDiscountList().get(0).getEndTm();
                    preBookListBean.setActivityDiscountEndDate(activityDiscountEndDate);
                }
            }

        }
    }

    private void hmFeeInfoSet(Map<String, HighProductFeeRateBean> highProductFeeRateMap, Map<String, HighProductActiDiscountListBean> highProductActiDiscountListMap, PreBookListBean preBookListBean) {
        if ("0".equals(convertBusiType(preBookListBean.getTradeType()))) {
            HighProductFeeRateBean highProductFeeRateBean = highProductFeeRateMap.get(preBookListBean.getPreId());
            BigDecimal appointAmt = getAppointAmt(highProductFeeRateBean, preBookListBean);
            if (highProductFeeRateBean != null) {
                preBookListBean.setFee(getFee(appointAmt, preBookListBean, highProductFeeRateBean));
                preBookListBean.setFeeRate(highProductFeeRateBean.getFeeRate());
                preBookListBean.setFeeRateMethod(highProductFeeRateBean.getGetFeeRateMethod());
                preBookListBean.setDisCountFee(calFee(appointAmt, null, highProductFeeRateBean,preBookListBean.getDiscountUseType(),BigDecimal.ZERO));
            }
            //活动截止日
            HighProductActiDiscountListBean highProductActiDiscountListBean = highProductActiDiscountListMap.get(preBookListBean.getFundCode());
            if (highProductActiDiscountListBean != null && "1".equals(highProductActiDiscountListBean.getActiFlag())) {
                if (!CollectionUtils.isEmpty(highProductActiDiscountListBean.getDiscountList())) {
                    String activityDiscountEndDate = highProductActiDiscountListBean.getDiscountList().get(0).getEndDt() + highProductActiDiscountListBean.getDiscountList().get(0).getEndTm();
                    preBookListBean.setActivityDiscountEndDate(activityDiscountEndDate);
                }
            }

        }
    }

    private BigDecimal getDisCountFee(BigDecimal appointAmt, PreBookListBean preBookListBean, HighProductFeeRateBean highProductFeeRateBean) {
        if (FeeCalModeEnum.INNER.getCode().equals(preBookListBean.getFeeCalMode())) {
            logger.info("内扣,手续费为0");
            return BigDecimal.ZERO;
        }
        if (!DisCodeEnum.HZ.getCode().equals(preBookListBean.getDisCode())) {
            return calFee(appointAmt, null, highProductFeeRateBean,preBookListBean.getDiscountUseType(),BigDecimal.ZERO);
        } else {
            if (preBookListBean.getPeDivideCallFlag().equals(YesOrNoEnum.YES.getCode())) {
                if (GetFeeRateMethodEnum.SUBS.getCode().equals(highProductFeeRateBean.getGetFeeRateMethod())) {
                    // 分次call,按认缴,非首次,费用为0
                    if (YesOrNoEnum.NO.getCode().equals(preBookListBean.getFirstPreId())) {
                        logger.info("分次call,按认缴,非首次,费用为0,preId={}", preBookListBean.getPreId());
                        return BigDecimal.ZERO;
                    } else {
                        // 分次call,按认缴,首次,费用为认缴计算金额
                        logger.info("分次call,按认缴,首次,费用为认缴计算金额,preId={}", preBookListBean.getPreId());
                        return calFee(appointAmt, null, highProductFeeRateBean,preBookListBean.getDiscountUseType(),BigDecimal.ZERO);
                    }

                } else {
                    // 分次call,按实缴,费用为实缴计算金额
                    logger.info("分次call,按实缴,费用为实缴计算金额,preId={}", preBookListBean.getPreId());
                    return calFee(appointAmt, null, highProductFeeRateBean,preBookListBean.getDiscountUseType(),BigDecimal.ZERO);
                }
            } else {
                // 非分次call,按照预约金额计算金额
                logger.info("非分次call,按照预约金额计算金额,preId={}", preBookListBean.getPreId());
                return calFee(appointAmt, null, highProductFeeRateBean,preBookListBean.getDiscountUseType(),BigDecimal.ZERO);
            }
        }
    }

    private BigDecimal getFee(BigDecimal appointAmt, PreBookListBean preBookListBean, HighProductFeeRateBean highProductFeeRateBean) {
        if (FeeCalModeEnum.INNER.getCode().equals(preBookListBean.getFeeCalMode())) {
            logger.info("内扣,手续费为0");
            return BigDecimal.ZERO;
        }
        if (!DisCodeEnum.HZ.getCode().equals(preBookListBean.getDisCode())) {
            return calFee(appointAmt, preBookListBean.getDiscount(), highProductFeeRateBean,preBookListBean.getDiscountUseType(),preBookListBean.getDiscountAmt());
        } else {
            if (preBookListBean.getPeDivideCallFlag().equals(YesOrNoEnum.YES.getCode())) {
                if (GetFeeRateMethodEnum.SUBS.getCode().equals(highProductFeeRateBean.getGetFeeRateMethod())) {
                    // 分次call,按认缴,非首次,费用为0
                    if (YesOrNoEnum.NO.getCode().equals(preBookListBean.getFirstPreId())) {
                        logger.info("分次call,按认缴,非首次,费用为0,preId={}", preBookListBean.getPreId());
                        return BigDecimal.ZERO;
                    } else {
                        // 分次call,按认缴,首次,费用为认缴计算金额
                        logger.info("分次call,按认缴,首次,费用为认缴计算金额,preId={}", preBookListBean.getPreId());
                        return calFee(appointAmt, preBookListBean.getDiscount(), highProductFeeRateBean,preBookListBean.getDiscountUseType(),preBookListBean.getDiscountAmt());
                    }

                } else {
                    // 分次call,按实缴,费用为实缴计算金额
                    logger.info("分次call,按实缴,费用为实缴计算金额,preId={}", preBookListBean.getPreId());
                    return calFee(appointAmt, preBookListBean.getDiscount(), highProductFeeRateBean,preBookListBean.getDiscountUseType(),preBookListBean.getDiscountAmt());
                }
            } else {
                // 非分次call,按照预约金额计算金额
                logger.info("非分次call,按照预约金额计算金额,preId={}", preBookListBean.getPreId());
                return calFee(appointAmt, preBookListBean.getDiscount(), highProductFeeRateBean,preBookListBean.getDiscountUseType(),preBookListBean.getDiscountAmt());
            }
        }
    }

    /**
     * 获取计算手续费的预约金额
     *
     * @param highProductFeeRateBean 费率信息
     * @param preBookListBean        预约信息
     * @return
     */
    private BigDecimal getAppointAmt(HighProductFeeRateBean highProductFeeRateBean, PreBookListBean preBookListBean) {
        BigDecimal appointAmt = preBookListBean.getAckAmt();
        if (!DisCodeEnum.HZ.getCode().equals(preBookListBean.getDisCode())) {
            logger.info("不是好臻的,计算预约费用直接用预约金额,preBookListBean={}", JSON.toJSONString(preBookListBean));
            return appointAmt;
        }
        if (highProductFeeRateBean == null) {
            logger.info("查不到费率,计算预约费用直接用预约金额");
            return appointAmt;
        }
        if (GetFeeRateMethodEnum.SUBS.getCode().equals(highProductFeeRateBean.getGetFeeRateMethod()) && preBookListBean.getAppointSubsAmt() != null) {
            appointAmt = preBookListBean.getAppointSubsAmt();
        }
        return appointAmt;
    }

    /**
     * buildPreBookInfo:(补充预约信息)
     *
     * @param rstList
     * @param buyProductAppointmentMap
     * @param sellProductAppointmentMap
     * <AUTHOR>
     * @date 2018年1月9日 下午3:25:01
     */
    private void buildSupplyPreBookInfo(List<PreBookListBean> rstList, Map<String, ProductAppointmentInfoBean> buyProductAppointmentMap, Map<String, ProductAppointmentInfoBean> sellProductAppointmentMap) {
        //计算打款截止日，预约开放日
        for (PreBookListBean preBookListBean : rstList) {

            //计算购买费率
            if ("0".equals(convertBusiType(preBookListBean.getTradeType()))) {
                ProductAppointmentInfoBean productAppointmentInfoBean = buyProductAppointmentMap.get(preBookListBean.getPreId());
                logger.info("productCode:{},productAppointmentInfoBean:{}", preBookListBean.getFundCode(), JSON.toJSONString(productAppointmentInfoBean));
                if (productAppointmentInfoBean != null) {
                    preBookListBean.setPayEndDate(productAppointmentInfoBean.getPayDeadlineDtm());
                    preBookListBean.setOpenStartDt(productAppointmentInfoBean.getOpenStartDt());
                    preBookListBean.setOpenEndDt(productAppointmentInfoBean.getOpenEndDt());
                    preBookListBean.setmBusiCode(productAppointmentInfoBean.getmBusiCode());

                }

            } else if ("1".equals(convertBusiType(preBookListBean.getTradeType()))) {
                ProductAppointmentInfoBean productAppointmentInfoBean = sellProductAppointmentMap.get(preBookListBean.getPreId());
                logger.info("productCode:{},productAppointmentInfoBean:{}", preBookListBean.getFundCode(), JSON.toJSONString(productAppointmentInfoBean));
                if (productAppointmentInfoBean != null) {
                    preBookListBean.setPayEndDate(productAppointmentInfoBean.getPayDeadlineDtm());
                    preBookListBean.setOpenStartDt(productAppointmentInfoBean.getOpenStartDt());
                    preBookListBean.setOpenEndDt(productAppointmentInfoBean.getOpenEndDt());
                    preBookListBean.setmBusiCode(productAppointmentInfoBean.getmBusiCode());
                }
            }

        }
    }

    /**
     * buildProductAppointmentMap:(构造预约对象map)
     *
     * @param productAppointmentMap
     * <AUTHOR>
     * @date 2018年1月9日 下午3:02:11
     */
    private void buildProductAppointmentMap(Map<String, ProductAppointmentInfoBean> productAppointmentMap, HighProductBaseInfoBean highProductBaseBean, String preId) {

        ProductAppointmentInfoBean productAppointmentInfoBean = new ProductAppointmentInfoBean();
        productAppointmentInfoBean.setProductId(highProductBaseBean.getFundCode());
        productAppointmentInfoBean.setShareClass(highProductBaseBean.getShareClass());
        productAppointmentInfoBean.setDisCode(DisCodeEnum.HM.getCode());
        productAppointmentMap.put(preId, productAppointmentInfoBean);
    }

    /**
     * buildHighFeeRateMap:(构造费率map)
     *
     * @param highProductFeeRateMap
     * <AUTHOR>
     * @date 2018年1月9日 下午2:34:56
     */
    private void buildHighFeeRateMap(Map<String, HighProductFeeRateBean> highProductFeeRateMap, HighProductStatInfoBean highProductStatInfoBean, PreBookListBean preBookListBean) {
        //构造费率map
        String busiType = convertBusiType(preBookListBean.getTradeType());
        if ("0".equals(busiType)) {
            if ("1".equals(preBookListBean.getSupportAdvanceFlag())) {
                HighProductFeeRateBean highProductFeeRateBean = new HighProductFeeRateBean();
                // 这里取不到mBusiCode，后面查询费率时重新赋值busiCode
                highProductFeeRateBean.setBusiCode(getBuyBusiCode(preBookListBean.getmBusiCode()));
                highProductFeeRateBean.setInvstType(preBookListBean.getInvestType());
                highProductFeeRateBean.setFundCode(preBookListBean.getFundCode());
                highProductFeeRateBean.setShareClass(preBookListBean.getShareClass());
                highProductFeeRateMap.put(preBookListBean.getPreId(), highProductFeeRateBean);
            } else {
                HighProductFeeRateBean highProductFeeRateBean = new HighProductFeeRateBean();
                if (highProductStatInfoBean != null) {
                    highProductFeeRateBean.setBusiCode(convertToBusiCode(highProductStatInfoBean.getBuyStatus()));
                } else {
                    highProductFeeRateBean.setBusiCode(convertToBusiCode(null));
                }

                highProductFeeRateBean.setInvstType(preBookListBean.getInvestType());
                highProductFeeRateBean.setFundCode(preBookListBean.getFundCode());
                highProductFeeRateBean.setShareClass(preBookListBean.getShareClass());
                highProductFeeRateMap.put(preBookListBean.getPreId(), highProductFeeRateBean);
            }

        }
    }

    /**
     * buildPreBookListBean:(构建预约基本信息)
     *
     * @param preBookBean
     * @param preBookListBean
     * @param queryAccHboneInfoResult
     * @param highProductBaseBean
     * <AUTHOR>
     * @date 2018年1月9日 下午4:29:16
     */
    private void buildPreBookListBean(PreBookBean preBookBean, PreBookListBean preBookListBean, QueryAccHboneInfoResult queryAccHboneInfoResult, HighProductBaseInfoBean highProductBaseBean, QueryTxAcctByHboneResult queryTxAcctByHboneResult) {
        preBookListBean.setPreId(preBookBean.getPreId());
        preBookListBean.setHboneNo(preBookBean.getHboneNo());
        SubscribeAmtDetailPo subscribeAmtDetail = null;
        if (queryTxAcctByHboneResult != null) {
            preBookListBean.setCustNo(queryTxAcctByHboneResult.getTxAcctNo());
            subscribeAmtDetail = subscribeAmtDetailRepository.getSubscribeAmtDetail(queryTxAcctByHboneResult.getTxAcctNo(), highProductBaseBean.getFundCode());
        }

        if (queryAccHboneInfoResult != null) {
            preBookListBean.setInvestType(queryAccHboneInfoResult.getUserType());
        }

        preBookListBean.setCustName(preBookBean.getCustName());
        preBookListBean.setFundCode(preBookBean.getFundCode());
        preBookListBean.setFundName(highProductBaseBean.getFundAttr());
        preBookListBean.setBankAcctNo(preBookBean.getBankAcctNo());
        preBookListBean.setOrderId(preBookBean.getOrderId());
        preBookListBean.setTradeType(preBookBean.getTradeType());
        preBookListBean.setIdType(preBookBean.getIdType());
        preBookListBean.setIdNo(preBookBean.getIdNo());
        preBookListBean.setPrebookState(preBookBean.getPrebookState());
        preBookListBean.setAckAmt(preBookBean.getAckAmt());
        preBookListBean.setSellVol(preBookBean.getSellVol());
        preBookListBean.setFee(preBookBean.getFee());
        preBookListBean.setCreDt(preBookListBean.getCreDt());
        preBookListBean.setSupportAdvanceFlag(convertSupportAdvanceFlag(highProductBaseBean.getIsScheduledTrade()));
        preBookListBean.setShareClass(highProductBaseBean.getShareClass());
        preBookListBean.setDiscount(preBookBean.getDiscountRate());
        preBookListBean.setCreDt(preBookBean.getCreDt());
        // 双录完成时间
        preBookListBean.setDoubleHandleDt(preBookBean.getDoubleHandleDt());
        // 双录处理标识0-无需双录 1-未双录 2-已双录
        preBookListBean.setDoubleHandleFlag(preBookBean.getDoubleHandleFlag());
        // 是否需要双录标识 0-否 1-是
        preBookListBean.setDoubleNeedFlag(preBookBean.getDoubleNeedFlag());
        // 是否首次实缴标识 0-否 1-是
        preBookListBean.setFirstPreId(preBookBean.getFirstPreId());
        // 预约认缴金额
        preBookListBean.setAppointSubsAmt(preBookBean.getSubsAmt());
        // 认缴金额
        if (subscribeAmtDetail != null) {
            preBookListBean.setSubsAmt(subscribeAmtDetail.getSubscribeAmt());
        }
        preBookListBean.setCurrency(preBookBean.getCurrency());

    }

    /**
     * queryProductAppointmentMap:(查询预约信息map)
     *
     * @param productAppointmentMap
     * <AUTHOR>
     * @date 2018年1月9日 下午2:18:50
     */
    private void queryProductAppointmentMap(Map<String, ProductAppointmentInfoBean> productAppointmentMap, String busiType, Date appDtm) {
        //查询产品基本信息
        CountDownLatch latch = new CountDownLatch(productAppointmentMap.size());
        for (ProductAppointmentInfoBean productAppointmentInfoBean : productAppointmentMap.values()) {
            CommonThreadPool.submit(new QueryProductAppointmentInfoTask(queryHighProductOuterService, productAppointmentInfoBean, appDtm, busiType, latch));
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryPreBookListFacadeService|QueryProductAppointmentInfoTask|latch.await exception.", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * querHighProductActiDiscountMap:(查询高端产品活动折扣)
     *
     * @param highProductActiDiscountMap
     * <AUTHOR>
     * @date 2018年1月25日 上午11:40:47
     */
    private void querHighProductActiDiscountMap(Map<String, HighProductActiDiscountListBean> highProductActiDiscountMap) {
        // //查询产品基本信息
        CountDownLatch latch = new CountDownLatch(highProductActiDiscountMap.size());
        for (Map.Entry<String, HighProductActiDiscountListBean> entry : highProductActiDiscountMap.entrySet()) {
            HighProductActiDiscountListBean highProductActiDiscountListBean = entry.getValue();
            QueryHighActiDiscountContext queryHighActiDiscountContext = new QueryHighActiDiscountContext();
            queryHighActiDiscountContext.setMidProductId(entry.getKey());
            CommonThreadPool.submit(new QueryHighActiDiscountTask(queryHighProductOuterService, highProductActiDiscountListBean, queryHighActiDiscountContext, latch));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryPreBookListFacadeService|QueryProductAppointmentInfoTask|latch.await exception.", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * getProductBaseInfoMap:(创建产品基本信息map)
     *
     * @param productMap
     * <AUTHOR>
     * @date 2018年1月9日 上午10:10:23
     */
    private void queryProductBaseInfoMap(Map<String, HighProductBaseInfoBean> productMap) {
        //查询产品基本信息
        CountDownLatch productCountDownLatch = new CountDownLatch(productMap.size());
        for (HighProductBaseInfoBean highProductBaseBean : productMap.values()) {
            CommonThreadPool.submit(new QueryProductInfoTask(queryHighProductOuterService, highProductBaseBean, productCountDownLatch));
        }

        try {
            productCountDownLatch.await();
        } catch (InterruptedException e) {
            logger.error("QueryPreBookListFacadeService|productCountDownLatch.await exception.", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * createProductStatMap:(查询产品状态map)
     *
     * @param highProductStatInfoMap
     * @param taTradeDt
     * <AUTHOR>
     * @date 2018年1月9日 上午10:14:04
     */
    private void queryProductStatMap(Map<String, HighProductStatInfoBean> highProductStatInfoMap, String taTradeDt) {
        //批量查询产品状态
        List<HighProductStatInfoBean> highProductStatList = queryHighProductOuterService.getBatchProductStatInfo(new ArrayList<String>(highProductStatInfoMap.keySet()), taTradeDt);
        if (!CollectionUtils.isEmpty(highProductStatList)) {
            for (HighProductStatInfoBean highProductStatInfoBean : highProductStatList) {
                highProductStatInfoMap.put(highProductStatInfoBean.getFundCode(), highProductStatInfoBean);
            }
        }

    }

    /**
     * queryHighProductFeeRateMap:(查询高端产品费率map)
     *
     * @param highProductFeeRateMap
     * <AUTHOR>
     * @date 2018年1月9日 上午10:25:10
     */
    private void queryHighProductFeeRateMap(Map<String, HighProductFeeRateBean> highProductFeeRateMap, List<PreBookListBean> rstList) {
        CountDownLatch highProductFeeRateLatch = new CountDownLatch(rstList.size());
        for (PreBookListBean preBookListBean : rstList) {
            HighProductFeeRateBean highProductFeeRateBean = highProductFeeRateMap.get(preBookListBean.getPreId());
            if (highProductFeeRateBean != null && "1".equals(preBookListBean.getSupportAdvanceFlag()) && StringUtils.isNotBlank(preBookListBean.getmBusiCode())) {
                // 前面创建bean时取不到mBusiCode，设置的busiCode可能不对，这里重新赋值
                highProductFeeRateBean.setBusiCode(getBuyBusiCode(preBookListBean.getmBusiCode()));
                if (DisCodeEnum.HZ.getCode().equals(preBookListBean.getDisCode())) {
                    highProductFeeRateBean.setBusiCode(BusinessCodeEnum.SUBSCRIBE.getCode());
                }
            }
            BigDecimal feeBaseAmt = preBookListBean.getAckAmt();
            if (DisCodeEnum.HZ.getCode().equals(preBookListBean.getDisCode()) && preBookListBean.getSubsAmt() != null) {
                feeBaseAmt = preBookListBean.getSubsAmt();
            }
            CommonThreadPool.submit(new QueryProductFeeRateTask(queryHighProductOuterService, highProductFeeRateBean, feeBaseAmt, highProductFeeRateLatch));
        }

        try {
            highProductFeeRateLatch.await();
        } catch (Exception e) {
            logger.error("QueryPreBookListFacadeService|QueryProductFeeRateTask|highProductFeeRateLatch|error", e);
        }
    }

    /**
     * queryHboneNoAndTxAcctNoMap:(查询一帐通号和交易账号的对应map)
     *
     * @param hboneNoAndTxAcctNoMap
     * <AUTHOR>
     * @date 2018年1月9日 上午10:46:37
     */
    private void queryHboneNoAndTxAcctNoMap(Map<String, QueryTxAcctByHboneResult> hboneNoAndTxAcctNoMap, BaseRequest request) {
        /**
         * 查询一帐通号和交易账号的对应map
         */
        CountDownLatch latch = new CountDownLatch(hboneNoAndTxAcctNoMap.size());
        for (QueryTxAcctByHboneResult queryTxAcctByHboneResult : hboneNoAndTxAcctNoMap.values()) {

            CommonThreadPool.submit(new QueryTxAcctNoByHbOneNoTask(queryHbOneNoOuterService, queryTxAcctByHboneResult, latch, request));
        }

        try {
            latch.await();
        } catch (Exception e) {
            logger.error("QueryPreBookListFacadeService|QueryTxAcctNoByHbOneNoTask|latch|error", e);
        }
    }

    /**
     * queryHbOneNoInfoMap:(查询一帐通信息)
     *
     * @param hbOneNoInfoMap
     * <AUTHOR>
     * @date 2018年1月9日 下午1:58:29
     */
    private void queryHbOneNoInfoMap(Map<String, QueryAccHboneInfoResult> hbOneNoInfoMap) {
        /**
         * 查询一帐通信息map
         */
        CountDownLatch latch = new CountDownLatch(hbOneNoInfoMap.size());
        for (QueryAccHboneInfoResult queryAccHboneInfoResult : hbOneNoInfoMap.values()) {

            CommonThreadPool.submit(new QuerySingleTask(queryAccHboneInfoOuterService, queryAccHboneInfoResult, latch));
        }

        try {
            latch.await();
        } catch (Exception e) {
            logger.error("QueryPreBookListFacadeService|QuerySingleTask|latch|error", e);
        }
    }

    /**
     * createQueryContext:(创建查询条件)
     *
     * @param request
     * @return
     * <AUTHOR>
     * @date 2018年1月9日 上午9:49:02
     */
    private QueryPreBookListOuterContext createQueryContext(QueryPreBookListRequest request) {
        QueryPreBookListOuterContext queryContext = new QueryPreBookListOuterContext();
        queryContext.setHboneNo(request.getHbOneNo());
        queryContext.setPreId(request.getPreId());
        queryContext.setPrebookState(request.getPreBookState());
        queryContext.setUseFlag(request.getUseFlag());
        queryContext.setTradeType(request.getTradeType());
        queryContext.setPreType(request.getPreType());
        queryContext.setCustNo(request.getTxAcctNo());
        queryContext.setFundCode(request.getFundCode());
        queryContext.setDisCode(request.getDisCode());
        queryContext.setTradeState(Collections.singletonList(YesOrNoEnum.NO.getCode()));
        queryContext.setIdNo(request.getIdNo());
        queryContext.setCustName(request.getCustName());
        queryContext.setEndDt(request.getEndDt());
        queryContext.setStartDt(request.getStartDt());
        queryContext.setCurPage(request.getPageNo());
        queryContext.setPageSize(request.getPageSize());
        queryContext.setFundName(request.getFundName());
        return queryContext;

    }

    /**
     * getBuyBusiCode:(根据mBusiCode获取购买busiCode)
     *
     * @param mBusiCode
     * @return
     * <AUTHOR>
     * @date 2017年10月29日 下午4:00:44
     */
    private String getBuyBusiCode(String mBusiCode) {
        if (BusinessCodeEnum.getByMCode(mBusiCode) != null) {
            return BusinessCodeEnum.getByMCode(mBusiCode).getCode();
        } else {
            return BusiCodeEnum.PURCHASE.getCode();
        }
    }
}

