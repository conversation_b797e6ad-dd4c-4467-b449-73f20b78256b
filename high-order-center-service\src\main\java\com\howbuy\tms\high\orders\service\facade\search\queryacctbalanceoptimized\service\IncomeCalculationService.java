/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.service;

import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.StandardFixedIncomeFlagEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryasset.bean.HighFundAssetIncomeDomain;
import com.howbuy.tms.common.utils.BigUtil;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.constants.BalanceConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @description: 收益计算服务
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
@Service
public class IncomeCalculationService {

    private static final Logger logger = LogManager.getLogger(IncomeCalculationService.class);

    /**
     * @description: 设置持仓收益信息
     * @param balanceBean 持仓Bean
     * @param assetDto 资产收益信息
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public void setBalanceAssetInfo(OptimizedBalanceBean balanceBean, HighFundAssetIncomeDomain assetDto) {
        if (assetDto == null) {
            return;
        }

        String productSubType = balanceBean.getProductSubType();
        String standardFixedIncomeFlag = balanceBean.getStandardFixedIncomeFlag();

        // 债券固收产品特殊处理
        if (StandardFixedIncomeFlagEnum.BOND_GS.getCode().equals(standardFixedIncomeFlag)) {
            balanceBean.setIncomeDt(assetDto.getIncomeDt());
        } else {
            // 设置当前收益
            balanceBean.setCurrentAssetCurrency(BigUtil.formatMoney(assetDto.getCurrentAsset(), BalanceConstants.Numbers.MONEY_SCALE));
            balanceBean.setCurrentIncome(BigUtil.formatMoney(assetDto.getCurrentAssetRmb(), BalanceConstants.Numbers.MONEY_SCALE));
            
            // 设置日收益
            balanceBean.setDailyAssetCurrency(BigUtil.formatMoney(assetDto.getDailyAsset(), BalanceConstants.Numbers.MONEY_SCALE));
            balanceBean.setDailyAsset(BigUtil.formatMoney(assetDto.getDailyAssetRmb(), BalanceConstants.Numbers.MONEY_SCALE));
            
            // 设置收益日期
            balanceBean.setIncomeDt(assetDto.getIncomeDt());
            
            // 设置已实现收益
            balanceBean.setAccumRealizedIncome(MoneyUtil.formatMoney(assetDto.getAccumRealizedIncome(), BalanceConstants.Numbers.MONEY_SCALE));
            balanceBean.setAccumRealizedIncomeRmb(MoneyUtil.formatMoney(assetDto.getAccumRealizedIncomeRmb(), BalanceConstants.Numbers.MONEY_SCALE));
            
            // 设置累计收益
            balanceBean.setAccumIncome(BigUtil.formatMoney(assetDto.getAccumIncome(), BalanceConstants.Numbers.MONEY_SCALE));
            balanceBean.setAccumIncomeRmb(BigUtil.formatMoney(assetDto.getAccumIncomeRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        }

        // 设置持仓成本
        balanceBean.setBalanceCost(MoneyUtil.formatMoney(assetDto.getBalanceCostRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(assetDto.getBalanceCost(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置新收益字段
        balanceBean.setBalanceIncomeNew(MoneyUtil.formatMoney(assetDto.getBalanceIncomeNew(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceIncomeNewRmb(MoneyUtil.formatMoney(assetDto.getBalanceIncomeNewRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumIncomeNew(MoneyUtil.formatMoney(assetDto.getAccumIncomeNew(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumIncomeNewRmb(MoneyUtil.formatMoney(assetDto.getAccumIncomeNewRmb(), BalanceConstants.Numbers.MONEY_SCALE));

        // 设置累计成本
        balanceBean.setAccumCost(MoneyUtil.formatMoney(assetDto.getAccumCost(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCostRmb(MoneyUtil.formatMoney(assetDto.getAccumCostRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置浮动收益
        balanceBean.setBalanceFloatIncome(MoneyUtil.formatMoney(assetDto.getBalanceFloatIncome(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceFloatIncomeRmb(MoneyUtil.formatMoney(assetDto.getBalanceFloatIncomeRmb(), BalanceConstants.Numbers.MONEY_SCALE));

        // 设置回款信息
        balanceBean.setAccumCollection(MoneyUtil.formatMoney(assetDto.getAccumCollection(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCollectionRmb(MoneyUtil.formatMoney(assetDto.getAccumCollectionRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setCashCollection(MoneyUtil.formatMoney(assetDto.getAccumCollection(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置持仓金额
        balanceBean.setBalanceAmt(MoneyUtil.formatMoney(assetDto.getBalanceAmt(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceAmtRmb(MoneyUtil.formatMoney(assetDto.getBalanceAmtRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置新成本字段
        balanceBean.setAccumCostNew(MoneyUtil.formatMoney(assetDto.getAccumCostNew(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCostRmbNew(MoneyUtil.formatMoney(assetDto.getAccumCostRmbNew(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置去费金额
        balanceBean.setBalanceAmtExFee(MoneyUtil.formatMoney(assetDto.getBalanceAmtExFee(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceAmtExFeeRmb(MoneyUtil.formatMoney(assetDto.getBalanceAmtExFeeRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置资产更新日期
        balanceBean.setAssetUpdateDate(assetDto.getUpdateDate());
        
        // 设置单位持仓成本去费
        balanceBean.setUnitBalanceCostExFee(MoneyUtil.formatMoney(assetDto.getUnitBalanceCostExFee(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(assetDto.getUnitBalanceCostExFeeRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置收益率
        balanceBean.setBalanceFloatIncomeRate(BigUtil.formatMoneyRate(assetDto.getBalanceFloatIncomeRate(), 4));
        balanceBean.setDayAssetRate(BigUtil.formatMoneyRate(assetDto.getDayAssetRate(), 4));
        balanceBean.setDayIncomeGrowthRate(BigUtil.formatMoneyRate(assetDto.getDayIncomeGrowthRate(), 4));
        balanceBean.setYieldRate(BigUtil.formatMoney(assetDto.getCurrentRate(), 4)); // 设置收益率
        balanceBean.setAccumYieldRate(BigUtil.formatMoney(assetDto.getAccumIncomeRate(), 4)); // 累计收益率

        // NA产品费用处理
        if ("1".equals(balanceBean.getNaProductFeeType())) {
            balanceBean.setReceivManageFee(assetDto.getReceivManageFee());
            balanceBean.setReceivPreformFee(assetDto.getReceivPreformFee());
        }
    }

    /**
     * @description: 处理股权产品收益
     * @param balanceBean 持仓Bean
     * @param assetDto 资产收益信息
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public void processEquityProductIncome(OptimizedBalanceBean balanceBean, HighFundAssetIncomeDomain assetDto) {
        if (assetDto == null || !ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            return;
        }

        // 设置股权产品新收益字段
        balanceBean.setAccumIncomeNew(MoneyUtil.formatMoney(assetDto.getAccumIncomeNew(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumIncomeNewRmb(MoneyUtil.formatMoney(assetDto.getAccumIncomeNewRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceIncomeNew(MoneyUtil.formatMoney(assetDto.getBalanceIncomeNew(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setBalanceIncomeNewRmb(MoneyUtil.formatMoney(assetDto.getBalanceIncomeNewRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置成本信息
        balanceBean.setAccumCost(MoneyUtil.formatMoney(assetDto.getAccumCost(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCostNew(MoneyUtil.formatMoney(assetDto.getAccumCostNew(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCostRmb(MoneyUtil.formatMoney(assetDto.getAccumCostRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCostRmbNew(MoneyUtil.formatMoney(assetDto.getAccumCostRmbNew(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置回款信息
        balanceBean.setAccumCollection(MoneyUtil.formatMoney(assetDto.getAccumCollection(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setAccumCollectionRmb(MoneyUtil.formatMoney(assetDto.getAccumCollectionRmb(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 股权已回款金额取资产中心
        balanceBean.setCashCollection(MoneyUtil.formatMoney(assetDto.getAccumCollection(), BalanceConstants.Numbers.MONEY_SCALE));
        
        // 设置资产更新日期
        balanceBean.setAssetUpdateDate(assetDto.getUpdateDate());
        
        // 设置单位持仓成本去费
        balanceBean.setUnitBalanceCostExFee(MoneyUtil.formatMoney(assetDto.getUnitBalanceCostExFee(), BalanceConstants.Numbers.MONEY_SCALE));
        balanceBean.setUnitBalanceCostExFeeRmb(MoneyUtil.formatMoney(assetDto.getUnitBalanceCostExFee(), BalanceConstants.Numbers.MONEY_SCALE));
    }

    /**
     * @description: 计算收益状态
     * @param balanceBean 持仓Bean
     * @param crisisFundList 清盘产品列表
     * @return 收益计算状态
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    public String calculateIncomeCalStat(OptimizedBalanceBean balanceBean, java.util.List<String> crisisFundList) {
        String productSubType = balanceBean.getProductSubType();

        // 危机产品、股权产品默认计算完成
        if (crisisFundList.contains(balanceBean.getProductCode()) ||
                ProductDBTypeEnum.GUQUAN.getCode().equals(productSubType)) {
            return BalanceConstants.IncomeCalStat.FINISHED;
        }

        // 根据净值日期和收益日期判断
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(balanceBean.getNavDt()) && 
                org.apache.commons.lang3.StringUtils.isNotEmpty(balanceBean.getIncomeDt()) &&
                balanceBean.getNavDt().compareTo(balanceBean.getIncomeDt()) <= 0) {
            return BalanceConstants.IncomeCalStat.FINISHED;
        }

        return BalanceConstants.IncomeCalStat.PROCESSING;
    }
}
