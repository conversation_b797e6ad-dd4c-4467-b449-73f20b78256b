/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse;
import com.howbuy.tms.high.orders.service.BaseTestSuite;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

import static org.junit.Assert.*;

/**
 * @description: 查询客户持仓接口(优化版)测试类
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
public class QueryAcctBalanceOptimizedFacadeServiceTest extends BaseTestSuite {

    private static final Logger logger = LogManager.getLogger(QueryAcctBalanceOptimizedFacadeServiceTest.class);

    @Autowired
    private QueryAcctBalanceOptimizedFacadeService queryAcctBalanceOptimizedFacadeService;

    /**
     * @description: 测试基本查询功能
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    @Test
    public void testExecuteBasic() {
        logger.info("开始测试基本查询功能");

        // 构建请求参数
        QueryAcctBalanceOptimizedRequest request = new QueryAcctBalanceOptimizedRequest();
        setCommonParams(request);
        request.setTxAcctNo("1100875141"); // 使用测试账号
        request.setDisCodeList(Arrays.asList("HM"));
        request.setBalanceStatus("1"); // 查询持仓

        // 执行查询
        QueryAcctBalanceOptimizedResponse response = queryAcctBalanceOptimizedFacadeService.execute(request);

        // 验证结果
        assertNotNull("响应对象不能为空", response);
        assertEquals("返回码应该为成功", ExceptionCodes.SUCCESS, response.getReturnCode());
        assertNotNull("持仓列表不能为空", response.getBalanceList());
        assertNotNull("在途产品列表不能为空", response.getUnconfirmeProducts());

        logger.info("查询结果：{}", JSON.toJSONString(response));
        logger.info("基本查询功能测试完成");
    }

    /**
     * @description: 测试参数校验
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    @Test
    public void testParameterValidation() {
        logger.info("开始测试参数校验");

        // 测试空参数
        QueryAcctBalanceOptimizedRequest request = new QueryAcctBalanceOptimizedRequest();
        setCommonParams(request);
        // 不设置交易账号和一账通号

        QueryAcctBalanceOptimizedResponse response = queryAcctBalanceOptimizedFacadeService.execute(request);

        // 验证结果
        assertNotNull("响应对象不能为空", response);
        assertEquals("返回码应该为成功", ExceptionCodes.SUCCESS, response.getReturnCode());
        assertTrue("持仓列表应该为空", response.getBalanceList().isEmpty());

        logger.info("参数校验测试完成");
    }

    /**
     * @description: 测试性能对比
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    @Test
    public void testPerformance() {
        logger.info("开始测试性能");

        QueryAcctBalanceOptimizedRequest request = new QueryAcctBalanceOptimizedRequest();
        setCommonParams(request);
        request.setTxAcctNo("1100875141");
        request.setDisCodeList(Arrays.asList("HM"));

        // 执行多次查询测试性能
        int testCount = 10;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < testCount; i++) {
            QueryAcctBalanceOptimizedResponse response = queryAcctBalanceOptimizedFacadeService.execute(request);
            assertNotNull("响应对象不能为空", response);
        }

        long endTime = System.currentTimeMillis();
        long avgTime = (endTime - startTime) / testCount;

        logger.info("执行{}次查询，平均耗时：{}ms", testCount, avgTime);
        logger.info("性能测试完成");
    }

    /**
     * @description: 测试字段完整性
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    @Test
    public void testFieldCompleteness() {
        logger.info("开始测试字段完整性");

        QueryAcctBalanceOptimizedRequest request = new QueryAcctBalanceOptimizedRequest();
        setCommonParams(request);
        request.setTxAcctNo("1100875141");
        request.setDisCodeList(Arrays.asList("HM"));

        QueryAcctBalanceOptimizedResponse response = queryAcctBalanceOptimizedFacadeService.execute(request);

        // 验证响应字段
        assertNotNull("交易账号不能为空", response.getTxAcctNo());
        assertNotNull("分销机构代码不能为空", response.getDisCode());
        assertNotNull("分销机构代码列表不能为空", response.getDisCodeList());
        assertNotNull("总市值不能为空", response.getTotalMarketValue());
        assertNotNull("总收益计算状态不能为空", response.getTotalIncomCalStat());
        assertNotNull("是否持有好臻产品标识不能为空", response.getHasHZProduct());
        assertNotNull("是否持有香港产品标识不能为空", response.getHasHKProduct());

        // 如果有持仓数据，验证持仓字段
        if (!response.getBalanceList().isEmpty()) {
            QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean balanceBean = response.getBalanceList().get(0);
            assertNotNull("产品代码不能为空", balanceBean.getProductCode());
            assertNotNull("产品名称不能为空", balanceBean.getProductName());
            assertNotNull("产品类型不能为空", balanceBean.getProductType());
            assertNotNull("产品子类型不能为空", balanceBean.getProductSubType());
            assertNotNull("规模类型不能为空", balanceBean.getScaleType());
        }

        logger.info("字段完整性测试完成");
    }

    /**
     * @description: 测试异常处理
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    @Test
    public void testExceptionHandling() {
        logger.info("开始测试异常处理");

        QueryAcctBalanceOptimizedRequest request = new QueryAcctBalanceOptimizedRequest();
        setCommonParams(request);
        request.setTxAcctNo("INVALID_ACCOUNT"); // 使用无效账号

        try {
            QueryAcctBalanceOptimizedResponse response = queryAcctBalanceOptimizedFacadeService.execute(request);
            assertNotNull("响应对象不能为空", response);
            // 即使账号无效，也应该返回成功状态，只是数据为空
            assertEquals("返回码应该为成功", ExceptionCodes.SUCCESS, response.getReturnCode());
        } catch (Exception e) {
            logger.error("异常处理测试失败", e);
            fail("不应该抛出异常");
        }

        logger.info("异常处理测试完成");
    }
}
