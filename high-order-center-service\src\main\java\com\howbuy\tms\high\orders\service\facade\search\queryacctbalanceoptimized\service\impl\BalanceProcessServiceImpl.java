/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.service.impl;

import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.common.enums.busi.IncomeCalStatEnum;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.OptimizedBalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized.QueryAcctBalanceOptimizedResponse.UnconfirmeProduct;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.constants.BalanceConstants;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.converter.BalanceConverter;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceoptimized.service.BalanceProcessService;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.service.custbooks.CustBooksService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 持仓业务处理服务实现
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
@Service
public class BalanceProcessServiceImpl implements BalanceProcessService {

    private static final Logger logger = LogManager.getLogger(BalanceProcessServiceImpl.class);

    @Autowired
    private CustBooksRepository custBooksRepository;

    @Autowired
    private CustBooksService custBooksService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Override
    public void processConsignmentBalance(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                                          List<OptimizedBalanceBean> balanceList, List<String> crisisFundList) {
        logger.info("开始处理代销资产，txAcctNo: {}, hbOneNo: {}", txAcctNo, hbOneNo);

        // 确保有交易账号
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
            request.setTxAcctNo(txAcctNo);
        }
        if (StringUtils.isEmpty(txAcctNo)) {
            logger.warn("处理代销资产时交易账号为空，hbOneNo: {}", hbOneNo);
            return;
        }

        // 确保有一账通号
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
            request.setHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            logger.warn("处理代销资产时一账通号为空，txAcctNo: {}", txAcctNo);
            return;
        }

        // 查询代销持仓数据
        List<String> productCodeList = StringUtils.isNotBlank(request.getProductCode()) ?
                Collections.singletonList(request.getProductCode()) : null;
        List<BalanceVo> consignmentBalanceList = custBooksRepository.selectBalanceWithLockPeriod(
                request.getDisCodeList(), txAcctNo, productCodeList, request.getBalanceStatus());

        if (CollectionUtils.isEmpty(consignmentBalanceList)) {
            logger.info("未查询到代销持仓数据");
            return;
        }

        // 批量查询产品信息
        Set<String> productCodes = consignmentBalanceList.stream()
                .map(BalanceVo::getProductCode)
                .collect(Collectors.toSet());
        Map<String, HighProductDBInfoBean> productInfoMap = queryHighProductOuterService
                .getHighProductDBInfoMap(new ArrayList<>(productCodes));

        // 处理非结构化产品(普通产品)
        processNotStructureProducts(request, txAcctNo, hbOneNo, consignmentBalanceList, productInfoMap,
                                    crisisFundList, balanceList);

        // 处理结构化产品(分期成立产品)
        processStructureProducts(request, txAcctNo, hbOneNo, consignmentBalanceList, productInfoMap,
                                 crisisFundList, balanceList);

        logger.info("代销资产处理完成，处理了{}条记录", consignmentBalanceList.size());
    }

    @Override
    public void processDirectBalance(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                                     List<OptimizedBalanceBean> balanceList, List<String> crisisFundList) {
        logger.info("开始处理直销资产，txAcctNo: {}, hbOneNo: {}", txAcctNo, hbOneNo);

        // 确保有一账通号
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            logger.warn("处理直销资产时一账通号为空，txAcctNo: {}", txAcctNo);
            return;
        }

        // TODO: 实现直销资产查询逻辑
        // 这里需要调用直销资产查询服务
        logger.info("直销资产处理完成");
    }

    @Override
    public void processTotalSummary(QueryAcctBalanceOptimizedResponse response, List<OptimizedBalanceBean> balanceList,
                                    QueryAcctBalanceOptimizedRequest request) {
        logger.info("开始汇总处理，持仓记录数: {}", balanceList.size());

        BigDecimal totalMarketValue = BigDecimal.ZERO;
        BigDecimal totalCurrentAsset = BigDecimal.ZERO;
        BigDecimal totalCashCollection = BigDecimal.ZERO;
        String totalIncomCalStat = BalanceConstants.IncomeCalStat.FINISHED;

        Set<String> processedFixedIncomeProducts = new HashSet<>();

        for (OptimizedBalanceBean bean : balanceList) {
            // 汇总市值
            if (bean.getMarketValue() != null && bean.isBalance()) {
                totalMarketValue = totalMarketValue.add(bean.getMarketValue());
            }

            // 千禧产品需要将待投金额累计进总资产
            if (BalanceConstants.ProductFlags.QIAN_XI_FLAG.equals(bean.getQianXiFlag()) &&
                    bean.getUnPaidInAmt() != null && bean.isBalance()) {
                totalMarketValue = totalMarketValue.add(bean.getUnPaidInAmt());
            }

            // 汇总收益（固收产品每个只汇总一次）
            if (bean.getCurrentIncome() != null) {
                if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(bean.getProductSubType())) {
                    if (!processedFixedIncomeProducts.contains(bean.getProductCode())) {
                        totalCurrentAsset = totalCurrentAsset.add(bean.getCurrentIncome());
                        processedFixedIncomeProducts.add(bean.getProductCode());
                    }
                } else {
                    totalCurrentAsset = totalCurrentAsset.add(bean.getCurrentIncome());
                }
            }

            // 汇总回款
            if (bean.getCashCollection() != null) {
                totalCashCollection = totalCashCollection.add(bean.getCashCollection());
            }

            // 收益计算状态
            if (BalanceConstants.IncomeCalStat.PROCESSING.equals(bean.getIncomeCalStat()) &&
                    BalanceConstants.IncomeCalStat.FINISHED.equals(totalIncomCalStat)) {
                totalIncomCalStat = BalanceConstants.IncomeCalStat.PROCESSING;
            }
        }

        // 设置汇总结果
        response.setTotalMarketValue(MoneyUtil.formatMoney(totalMarketValue, BalanceConstants.Numbers.MONEY_SCALE));
        response.setTotalCurrentAsset(MoneyUtil.formatMoney(totalCurrentAsset, BalanceConstants.Numbers.MONEY_SCALE));
        response.setTotalCashCollection(MoneyUtil.formatMoney(totalCashCollection, BalanceConstants.Numbers.MONEY_SCALE));
        response.setTotalIncomCalStat(totalIncomCalStat);

        logger.info("汇总处理完成，总市值: {}, 总收益: {}, 总回款: {}",
                response.getTotalMarketValue(), response.getTotalCurrentAsset(), response.getTotalCashCollection());
    }

    @Override
    public void filterBalanceInfo(QueryAcctBalanceOptimizedResponse response, List<OptimizedBalanceBean> balanceList,
                                  QueryAcctBalanceOptimizedRequest request) {
        logger.info("开始过滤持仓信息");

        // 检查是否持有好臻产品和香港产品
        boolean hasHZProduct = false;
        boolean hasHKProduct = false;

        for (OptimizedBalanceBean bean : balanceList) {
            // TODO: 实现好臻产品和香港产品的判断逻辑
            // 这里需要根据具体的业务规则来判断
        }

        response.setHasHZProduct(hasHZProduct ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        response.setHasHKProduct(hasHKProduct ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

        logger.info("持仓信息过滤完成");
    }

    @Override
    public void processOnWayAssets(QueryAcctBalanceOptimizedRequest request, List<String> disCodeList, String txAcctNo,
                                   QueryAcctBalanceOptimizedResponse response) {
        logger.info("开始处理在途资产");

        try {
            // 查询在途产品
            List<com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct> originalUnconfirmeProducts =
                    custBooksService.getTotalUnconfirmedAmtByQueryAcctBalance(
                            request.getHkSaleFlag(), request.getProductSubType(), disCodeList, txAcctNo,
                            request.getNotFilterHkFund(), request.getNotFilterHzFund());

            // 转换为优化版的在途产品对象
            List<UnconfirmeProduct> unconfirmeProducts = convertUnconfirmeProducts(originalUnconfirmeProducts);
            response.setUnconfirmeProducts(unconfirmeProducts);

            // 统计在途信息
            BigDecimal totalUnconfirmedAmt = BigDecimal.ZERO;
            int totalUnconfirmedNum = 0;
            int redeemUnconfirmedNum = 0;

            for (UnconfirmeProduct product : unconfirmeProducts) {
                if (product.getUnconfirmedAmt() != null) {
                    totalUnconfirmedAmt = totalUnconfirmedAmt.add(product.getUnconfirmedAmt());
                }
                totalUnconfirmedNum++;
                // TODO: 根据业务规则判断是否为赎回在途
            }

            response.setTotalUnconfirmedAmt(totalUnconfirmedAmt);
            response.setTotalUnconfirmedNum(totalUnconfirmedNum);
            response.setRedeemUnconfirmedNum(redeemUnconfirmedNum);

            logger.info("在途资产处理完成，在途产品数: {}, 在途总金额: {}", unconfirmeProducts.size(), totalUnconfirmedAmt);

        } catch (Exception e) {
            logger.error("处理在途资产异常: {}", e.getMessage(), e);
            // 设置默认值
            response.setUnconfirmeProducts(new ArrayList<>());
            response.setTotalUnconfirmedAmt(BigDecimal.ZERO);
            response.setTotalUnconfirmedNum(0);
            response.setRedeemUnconfirmedNum(0);
        }
    }

    @Override
    public void sortBalanceList(List<OptimizedBalanceBean> balanceList) {
        if (CollectionUtils.isNotEmpty(balanceList)) {
            Collections.sort(balanceList);
            logger.info("持仓列表排序完成");
        }
    }

    /**
     * @description: 计算收益计算状态
     * @param bean 持仓Bean
     * @param crisisFundList 清盘产品列表
     * @return 收益计算状态
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private String calculateIncomeCalStat(OptimizedBalanceBean bean, List<String> crisisFundList) {
        String productSubType = bean.getProductSubType();

        // 危机产品、股权产品默认计算完成
        if (crisisFundList.contains(bean.getProductCode()) ||
                ProductDBTypeEnum.GUQUAN.getCode().equals(productSubType)) {
            return BalanceConstants.IncomeCalStat.FINISHED;
        }

        // 根据净值日期和收益日期判断
        if (StringUtils.isNotEmpty(bean.getNavDt()) && StringUtils.isNotEmpty(bean.getIncomeDt()) &&
                bean.getNavDt().compareTo(bean.getIncomeDt()) <= 0) {
            return BalanceConstants.IncomeCalStat.FINISHED;
        }

        return BalanceConstants.IncomeCalStat.PROCESSING;
    }

    /**
     * @description: 转换在途产品对象
     * @param originalProducts 原始在途产品列表
     * @return 优化版在途产品列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private List<UnconfirmeProduct> convertUnconfirmeProducts(
            List<com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct> originalProducts) {
        List<UnconfirmeProduct> result = new ArrayList<>();

        for (com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct original : originalProducts) {
            UnconfirmeProduct optimized = new UnconfirmeProduct();
            optimized.setFundCode(original.getFundCode());
            optimized.setProductType(original.getProductType());
            optimized.setProductSubType(original.getProductSubType());
            optimized.setUnconfirmedAmt(original.getUnconfirmedAmt());
            optimized.setHkSaleFlag(original.getHkSaleFlag());
            optimized.setDisCode(original.getDisCode());
            result.add(optimized);
        }

        return result;
    }

    /**
     * @description: 处理非结构化产品(普通产品)
     * @param request 请求参数
     * @param txAcctNo 交易账号
     * @param hbOneNo 一账通号
     * @param balanceVoList 持仓数据列表
     * @param productInfoMap 产品信息Map
     * @param crisisFundList 清盘产品列表
     * @param balanceList 结果列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private void processNotStructureProducts(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                                             List<BalanceVo> balanceVoList, Map<String, HighProductDBInfoBean> productInfoMap,
                                             List<String> crisisFundList, List<OptimizedBalanceBean> balanceList) {

        // 分类产品代码集合
        Set<String> incomeFundCodeSet = new HashSet<>();
        Set<String> navFundCodeSet = new HashSet<>();
        Set<String> netBuyAmtFundCodeSet = new HashSet<>();
        Set<String> fractionateCallFundCodeSet = new HashSet<>();
        Set<String> fixedIncomeFundCodeSet = new HashSet<>();
        Set<String> notGSAndGQFundCodeSet = new HashSet<>();

        // 处理持仓数据并分类
        for (BalanceVo balanceVo : balanceVoList) {
            HighProductDBInfoBean productBean = productInfoMap.get(balanceVo.getProductCode());
            if (productBean == null) {
                logger.warn("产品信息不存在，productCode: {}", balanceVo.getProductCode());
                continue;
            }

            // 跳过分期成立产品，这些在processStructureProducts中处理
            if (isStageEstablishProduct(productBean)) {
                continue;
            }

            OptimizedBalanceBean optimizedBean = BalanceConverter.convertConsignmentBalance(
                    balanceVo, productBean, request.getDisCodeList());

            // 设置危机标识
            if (crisisFundList.contains(optimizedBean.getProductCode())) {
                optimizedBean.setCrisisFlag(BalanceConstants.ProductFlags.CRISIS_FLAG);
            }

            // 分类产品代码用于后续批量查询
            classifyProductCodes(productBean, optimizedBean.getProductCode(), incomeFundCodeSet, navFundCodeSet,
                    netBuyAmtFundCodeSet, fractionateCallFundCodeSet, fixedIncomeFundCodeSet, notGSAndGQFundCodeSet);

            balanceList.add(optimizedBean);
        }

        // 批量查询净值、收益等信息并设置到持仓对象中
        enrichBalanceInfo(request, txAcctNo, hbOneNo, balanceList, incomeFundCodeSet, navFundCodeSet,
                netBuyAmtFundCodeSet, fractionateCallFundCodeSet, fixedIncomeFundCodeSet, notGSAndGQFundCodeSet, crisisFundList);
    }

    /**
     * @description: 处理结构化产品(分期成立产品)
     * @param request 请求参数
     * @param txAcctNo 交易账号
     * @param hbOneNo 一账通号
     * @param balanceVoList 持仓数据列表
     * @param productInfoMap 产品信息Map
     * @param crisisFundList 清盘产品列表
     * @param balanceList 结果列表
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private void processStructureProducts(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                                          List<BalanceVo> balanceVoList, Map<String, HighProductDBInfoBean> productInfoMap,
                                          List<String> crisisFundList, List<OptimizedBalanceBean> balanceList) {

        // 获取分期成立产品代码列表
        List<String> stageProductCodeList = new ArrayList<>();
        for (BalanceVo balanceVo : balanceVoList) {
            HighProductDBInfoBean productBean = productInfoMap.get(balanceVo.getProductCode());
            if (productBean != null && isStageEstablishProduct(productBean)) {
                stageProductCodeList.add(balanceVo.getProductCode());
            }
        }

        if (CollectionUtils.isEmpty(stageProductCodeList)) {
            return;
        }

        // TODO: 实现分期成立产品的具体处理逻辑
        // 这里需要查询子账本表，处理分期成立的特殊逻辑
        logger.info("处理分期成立产品，产品数量: {}", stageProductCodeList.size());
    }

    /**
     * @description: 判断是否为分期成立产品
     * @param productBean 产品信息
     * @return 是否为分期成立产品
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private boolean isStageEstablishProduct(HighProductDBInfoBean productBean) {
        return "1".equals(productBean.getStageEstablishFlag());
    }

    /**
     * @description: 分类产品代码
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private void classifyProductCodes(HighProductDBInfoBean productBean, String productCode,
                                      Set<String> incomeFundCodeSet, Set<String> navFundCodeSet,
                                      Set<String> netBuyAmtFundCodeSet, Set<String> fractionateCallFundCodeSet,
                                      Set<String> fixedIncomeFundCodeSet, Set<String> notGSAndGQFundCodeSet) {

        String productSubType = productBean.getFundSubType();

        // 股权产品
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(productSubType)) {
            netBuyAmtFundCodeSet.add(productCode);
        } else {
            // 非股权产品需要查询收益
            incomeFundCodeSet.add(productCode);
            navFundCodeSet.add(productCode);
        }

        // 固收产品
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productSubType)) {
            fixedIncomeFundCodeSet.add(productCode);
        }

        // 分次call产品
        if ("1".equals(productBean.getFractionateCallFlag())) {
            fractionateCallFundCodeSet.add(productCode);
        }

        // 净值化产品
        if (isNavTypeProduct(productBean)) {
            notGSAndGQFundCodeSet.add(productCode);
        }
    }

    /**
     * @description: 判断是否为净值化产品
     * @param productBean 产品信息
     * @return 是否为净值化产品
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private boolean isNavTypeProduct(HighProductDBInfoBean productBean) {
        // 非股权或者净值化固定收益产品或者纯债固收
        return !ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())
                && !(ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())
                && ("3".equals(productBean.getStandardFixedIncomeFlag()) || "4".equals(productBean.getStandardFixedIncomeFlag())));
    }

    /**
     * @description: 丰富持仓信息(查询净值、收益等)
     * @author: hongdong.xie
     * @date: 2025/8/15 14:30
     * @since JDK 1.8
     */
    private void enrichBalanceInfo(QueryAcctBalanceOptimizedRequest request, String txAcctNo, String hbOneNo,
                                   List<OptimizedBalanceBean> balanceList, Set<String> incomeFundCodeSet,
                                   Set<String> navFundCodeSet, Set<String> netBuyAmtFundCodeSet,
                                   Set<String> fractionateCallFundCodeSet, Set<String> fixedIncomeFundCodeSet,
                                   Set<String> notGSAndGQFundCodeSet, List<String> crisisFundList) {

        // TODO: 实现批量查询净值、收益等信息的逻辑
        // 这里需要调用相应的服务查询净值、收益、实缴金额等信息
        logger.info("丰富持仓信息，持仓数量: {}", balanceList.size());
    }
}
