/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.facade.search.querybalancevoldtlforcrm;

import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.validator.highproductinfo.ProductInfoValidator;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmFacade;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmRequest;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmResponse;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.bean.BalanceVolDtlBean;
import com.howbuy.tms.high.orders.service.business.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * crm查询份额明细
 * <AUTHOR>
 * @date 2021/5/13 17:00
 * @since JDK 1.8
 */
@DubboService
@Service("queryBalanceVolDtlForCrmFacade")
public class QueryBalanceVolDtlForCrmFacadeService implements QueryBalanceVolDtlForCrmFacade {
    private static final Logger logger = LogManager.getLogger(QueryBalanceVolDtlForCrmFacadeService.class);

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmFacade.execute(QueryBalanceVolDtlForCrmRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryBalanceVolDtlForCrmFacadeService
     * @apiName execute
     * @apiDescription crm查询份额明细
     * @apiParam (请求参数) {String} fundCode
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=1&pageSize=9549&disCode=sT&txChannel=vNMBedZR&appTm=qZgxrihQ&disCodeList=SX58lTuN&fundCode=9bQtlewbE5&subOutletCode=TVJ1&pageNo=6028&operIp=cJwXNYEXQg&txAcctNo=DHKlwgi&appDt=dUj4Jwf&dataTrack=5ln4MM2X&txCode=2eP7qr&outletCode=FV
     * @apiSuccess (响应结果) {Array} balanceVolDtlList
     * @apiSuccess (响应结果) {Number} balanceVolDtlList.balanceVol 持仓份额
     * @apiSuccess (响应结果) {String} balanceVolDtlList.bankName 银行名称
     * @apiSuccess (响应结果) {String} balanceVolDtlList.bankAcctMask 银行卡号（按照掩码规则只给后六位）
     * @apiSuccess (响应结果) {String} balanceVolDtlList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} balanceVolDtlList.canRedeemDt 可赎回日期
     * @apiSuccess (响应结果) {String} balanceVolDtlList.openRedeDt 赎回开放日期（锁定到期日）
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"Pb2yy1B","balanceVolDtlList":[{"bankAcctMask":"bC","openRedeDt":"6y","balanceVol":2194.*************,"canRedeemDt":"c","bankName":"fkNhT9rq2","cpAcctNo":"e8pkRW"}],"totalPage":1378,"pageNo":8662,"description":"0jv6","totalCount":7396}
     */
    @Override
    public QueryBalanceVolDtlForCrmResponse execute(QueryBalanceVolDtlForCrmRequest request) {
        QueryBalanceVolDtlForCrmResponse response = new QueryBalanceVolDtlForCrmResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);

        String txAcctNo = request.getTxAcctNo();
        if (StringUtils.isEmpty(txAcctNo)) {
            if (StringUtils.isBlank(request.getHbOneNo())) {
                response.setDescription("一账通号为空");
                response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
                return response;
            }
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(request.getHbOneNo());
            if (StringUtils.isEmpty(txAcctNo)) {
                response.setDescription("查不到交易账号");
                response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
                return response;
            }
        }

        List<String> disCodeList = getDisCodeList(request);

        // 查询持仓
        List<BalanceVolDtlBean> beanList = queryBalanceList(txAcctNo, request.getFundCode(), disCodeList);
        response.setBalanceVolDtlList(beanList);

        // 设置卡信息
        setBankInfo(txAcctNo, disCodeList, request.getOutletCode(), beanList);

        return response;
    }

    /**
     * @param request
     * @return java.util.List<java.lang.String>
     * @description:(获取分销机构代码)
     * @author: haiguang.chen
     * @date: 2022/1/4 11:04
     * @since JDK 1.8
     */
    private List<String> getDisCodeList(QueryBalanceVolDtlForCrmRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }

    /**
     * 查询持仓
     *
     * @param txAcctNo
     * @param fundCode
     * @param disCodeList
     * @return java.util.List<com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.bean.BalanceVolDtlBean>
     * @author: huaqiang.liu
     * @date: 2021/5/17 11:00
     * @since JDK 1.8
     */
    private List<BalanceVolDtlBean> queryBalanceList(String txAcctNo, String fundCode, List<String> disCodeList) {
        Date now = new Date();
        String date = DateUtils.formatToString(now, DateUtils.YYYYMMDD);
        String time = DateUtils.formatToString(now, DateUtils.HHMMSS);
        String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(date, time);

        // 产品基本信息
        HighProductBaseInfoBean productInfo = queryHighProductOuterService.getHighProductBaseInfo(fundCode);
        // 校验高端产品信息是否存在
        ProductInfoValidator.validateProductInfoExsit(productInfo);

        // 赎回日期取最近的开放开始日
        String redeemDt = null;
        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(productInfo.getIsScheduledTrade())
                || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(productInfo.getIsScheduledTrade())) {
            ProductAppointmentInfoBean appointmentInfo = queryHighProductOuterService.queryAppointmentInfoByAppointDate(fundCode, "1", productInfo.getShareClass(), disCodeList.get(0), now);
            if (appointmentInfo != null) {
                if (taTradeDt.compareTo(appointmentInfo.getOpenStartDt()) < 0) {
                    redeemDt = appointmentInfo.getOpenStartDt();
                }
            }
        }

        // 查询持仓
        List<BalanceVo> booklist =  custBooksRepository.selectBalanceDtlWithOpenDt(disCodeList, txAcctNo, fundCode);
        List<BalanceVolDtlBean> beanList = new ArrayList<>(booklist.size());
        String finalRedeemDt = redeemDt;
        booklist.forEach(balance -> {
            BalanceVolDtlBean bean = new BalanceVolDtlBean();
            bean.setBalanceVol(balance.getBalanceVol());
            bean.setCpAcctNo(balance.getCpAcctNo());
            bean.setOpenRedeDt(balance.getOpenRedeDt());

            // 已过锁定期的才返回可赎回日期
            if (StringUtils.isBlank(balance.getOpenRedeDt()) || taTradeDt.compareTo(balance.getOpenRedeDt()) > 0) {
                bean.setCanRedeemDt(finalRedeemDt);
            }
            beanList.add(bean);
        });
        return beanList;
    }

    /**
     * 设置银行卡信息
     *
     * @param txAcctNo
     * @param disCodeList
     * @param beanList
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/5/14 16:32
     * @since JDK 1.8
     */
    private void setBankInfo(String txAcctNo, List<String> disCodeList, String outletCode, List<BalanceVolDtlBean> beanList) {
        if (beanList.isEmpty()) {
            return;
        }
        // 查询卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<String, QueryCustBankCardResult>();
        CountDownLatch latch = new CountDownLatch(beanList.size());
        beanList.forEach(bean -> {
            String cpAcct = bean.getCpAcctNo();
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, txAcctNo, disCodeList.get(0),
                    cpAcct, bankCardInfo, outletCode));
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryBalanceVolDtlForCrmFacadeService|setBankInfo|latch|{}", e.getMessage(), e);
            Thread.currentThread().interrupt();
        }

        // 设置银行卡信息
        beanList.forEach(bean -> {
            QueryCustBankCardResult cardResult = bankCardMap.get(bean.getCpAcctNo());
            bean.setBankName(cardResult.getBankName());
            bean.setBankAcctMask(PrivacyUtil.encryptBankAcct(cardResult.getBankAcct()));
        });
    }

}