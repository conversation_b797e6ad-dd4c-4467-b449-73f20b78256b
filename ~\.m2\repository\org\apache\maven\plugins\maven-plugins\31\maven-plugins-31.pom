<?xml version='1.0' encoding='UTF-8'?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>31</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven.plugins</groupId>
  <artifactId>maven-plugins</artifactId>
  <packaging>pom</packaging>

  <name>Apache Maven Plugins</name>
  <description>Maven Plugins</description>
  <url>https://maven.apache.org/plugins/</url>

  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/maven-plugins/</url>
  </ciManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/plugins-archives/</url>
    </site>
  </distributionManagement>

  <properties>
    <maven.site.path>plugins-archives/${project.artifactId}-LATEST</maven.site.path>
  </properties>

  <dependencies>
    <!-- dependencies to annotations -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-changes-plugin</artifactId>
          <configuration>
            <issueManagementSystems>
              <issueManagementSystem>JIRA</issueManagementSystem>
            </issueManagementSystems>
            <maxEntries>1000</maxEntries>
            <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
            <!-- Used by announcement-generate goal -->
            <templateDirectory>org/apache/maven/plugins</templateDirectory>
            <!-- Used by announcement-mail goal -->
            <subject>[ANN] ${project.name} ${project.version} Released</subject>
            <toAddresses>
              <toAddress implementation="java.lang.String"><EMAIL></toAddress>
              <toAddress implementation="java.lang.String"><EMAIL></toAddress>
            </toAddresses>
            <ccAddresses>
              <ccAddress implementation="java.lang.String"><EMAIL></ccAddress>
            </ccAddresses>
            <!-- These values need to be specified as properties in the profile apache-release in your settings.xml -->
            <fromDeveloperId>${apache.availid}</fromDeveloperId>
            <smtpHost>${smtp.host}</smtpHost>
          </configuration>
          <dependencies>
            <!-- Used by announcement-generate goal -->
            <dependency>
              <groupId>org.apache.maven.shared</groupId>
              <artifactId>maven-shared-resources</artifactId>
              <version>2</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <configuration>
            <tagletArtifacts>
              <tagletArtifact combine.id="org.apache.maven.plugin-tools:maven-plugin-tools-javadoc">
                <groupId>org.apache.maven.plugin-tools</groupId>
                <artifactId>maven-plugin-tools-javadoc</artifactId>
                <version>${mavenPluginToolsVersion}</version>
              </tagletArtifact>
            </tagletArtifacts>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/plugins/tags</tagBase>
            <releaseProfiles>apache-release,run-its</releaseProfiles>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${mavenPluginToolsVersion}</version>
          <executions>
            <execution>
              <id>default-descriptor</id>
              <phase>process-classes</phase>
            </execution>
            <execution>
              <id>generate-helpmojo</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <!-- publish mono-module site with "mvn site-deploy" -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <configuration>
            <skipDeploy>true</skipDeploy><!-- don't deploy site with maven-site-plugin -->
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <configuration>
            <content>${project.reporting.outputDirectory}</content><!-- no need for site:stage, use target/site -->
          </configuration>
          <executions>
            <execution>
              <id>scm-publish</id>
              <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
              <goals>
                <goal>publish-scm</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>enforce</goal>
            </goals>
            <id>ensure-no-container-api</id>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.codehaus.plexus:plexus-component-api</exclude>
                  </excludes>
                  <message>The new containers are not supported. You probably added a dependency that is missing the exclusions.</message>
                </bannedDependencies>
              </rules>
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>${mavenPluginToolsVersion}</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>quality-checks</id>
      <activation>
        <property>
          <name>quality-checks</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-docck-plugin</artifactId>
            <executions>
              <execution>
                <id>docck-check</id>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>run-its</id>
      <properties>
        <!-- when testing with JDK9, change these values to 1.6 from cmdline -->
        <invoker.maven.compiler.source>${maven.compiler.source}</invoker.maven.compiler.source>
        <invoker.maven.compiler.target>${maven.compiler.target}</invoker.maven.compiler.target>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <configuration>
              <debug>true</debug>
              <projectsDirectory>src/it</projectsDirectory>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <preBuildHookScript>setup</preBuildHookScript>
              <postBuildHookScript>verify</postBuildHookScript>
              <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              <settingsFile>src/it/settings.xml</settingsFile>
              <pomIncludes>
                <pomInclude>*/pom.xml</pomInclude>
              </pomIncludes>
              <properties>
                <maven.compiler.source>${invoker.maven.compiler.source}</maven.compiler.source>
                <maven.compiler.target>${invoker.maven.compiler.target}</maven.compiler.target>
              </properties>
            </configuration>
            <executions>
              <execution>
                <id>integration-test</id>
                <goals>
                  <goal>install</goal>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
