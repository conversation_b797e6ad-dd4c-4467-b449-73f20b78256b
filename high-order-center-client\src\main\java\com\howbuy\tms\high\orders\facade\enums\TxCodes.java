package com.howbuy.tms.high.orders.facade.enums;

/**
 * @Description:high-order-trade的交易code枚举
 * @Author: yun.lu
 * Date: 2025/7/17 14:11
 */
public class TxCodes {
    /** 查询高端交易订单接口 */
    public static final String QUERY_HIGH_DEAL_ORDER = "Z330001";
    /** 查询高端交易订单明细接口 */
    public static final String QUERY_HIGH_DEAL_ORDER_DTL = "Z330002";
    /** 查询高端基金认申购校验 */
    public static final String QUERY_SUBSORPUR_VALIDATE = "Z330003";
    /** 查询高端基金赎回校验 */
    public static final String QUERY_REDEEM_VALIDATE = "Z330004";
    /** 查询高端柜台基金修改分红方式校验 */
    public static final String QUERY_MODIFYDIV_VALIDATE = "Z330005";
    /** 查询高端交易撤单校验 */
    public static final String QUERY_FUND_CANCEL_VALIDATE = "Z330006";

    /** 高端网上个人客户基金认申购 */
    public static final String HIGH_SUBSORPUR_WEB = "Z330007";
    /** 高端柜台基金认申购 */
    public static final String HIGH_SUBSORPUR_COUNTER = "Z330008";
    /** 高端网上个人客户赎回基金 */
    public static final String HIGH_REDEEM_WEB = "Z330009";
    /** 高端柜台基金赎回 */
    public static final String HIGH_REDEEM_COUNTER = "Z330010";
    /** 高端网上个人客户修改分红方式 */
    public static final String HIGH_MODIFYDIV_WEB = "Z330011";
    /** 高端柜台基金修改分红方式 */
    public static final String HIGH_MODIFYDIV_COUNTER = "Z330012";
    /** 高端网站/柜台撤单 */
    public static final String HIGH_FUND_CANCEL = "Z330013";
    /** 高端网站/柜台强制撤单 */
    public static final String HIGH_FUND_FORCED_CANCEL = "Z330014";
    /** 查询高端产品持仓接口 */
    public static final String HIGH_FUND_QUERY_ACCT_BALANCE = "Z330015";
    /** 查询高端产品持仓明细接口 */
    public static final String HIGH_FUND_QUERY_ACCT_BALANCE_DTL = "Z330016";
    /** 新增电子签名接口 */
    public static final String HIGH_FUND_ADD_ESIGNATURE = "Z330017";
    /** 修改电子签名接口 */
    public static final String HIGH_FUND_MODIFY_ESIGNATURE = "Z330018";
    /** 查询电子签名接口 */
    public static final String HIGH_FUND_QUERY_ESIGNATURE = "Z330019";
    /** 查询高端订单列表 */
    public static final String QUERY_HIGH_DEAL_ORDER_LIST = "Z330020";
    /** 查询客户持有基金分红方式列表 */
    public static final String QUERY_CUST_FUND_DIV = "Z330021";
    /** 查询是否需要签署电子签名/电子合同 */
    public static final String QUERY_SIGN_ELEC_STATUS = "Z330022";
    /** 查询交易追加状态接口 */
    public static final String QUERY_SUPPLE_STATUS = "Z330023";
    /** 查询高端可撤单订单列表 */
    public static final String QUERY_HIGH_CAN_CANCEL_DEAL_ORDER_LIST = "Z330024";
    /** 查询客户已签订的电子合同 */
    public static final String QUERY_CUST_SIGNED_ECONTRACT = "Z330025";
    /** 查询客户在途资产 */
    public static final String QUERY_CUST_ONWAY_BALANCE = "Z330026";
    /** 查询高端可强撤订单列表 */
    public static final String QUERY_HIGH_CAN_FORCE_CANCEL_DEAL_ORDER_LIST = "Z330027";
    /** 查询产品额度信息 */
    public static final String QUERY_PRODUCT_QUOTA = "Z330028";
    /** 查询购买产品状态 */
    public static final String QUERY_BUY_FUND_STATUS = "Z330029";
    /** 查询赎回产品状态 */
    public static final String QUERY_REDEEM_FUND_STATUS = "Z330030";
    /** 查询赎回产品列表 */
    public static final String QUERY_REDEEM_FUND_LIST = "Z330031";
    /** 查询预约单列表 */
    public static final String QUERY_PRE_BOOK_LIST = "Z330032";
    /** 查询预约单详情 */
    public static final String QUERY_PRE_BOOK_DTL = "Z330033";
    /** 查询最近一条可使用预约单 */
    public static final String QUERY_LATELY_PRE_BOOK = "Z330034";
    /** 查询高端基金份额合并校验 */
    public static final String QUERY_SHARE_MERGE_VALIDATE = "Z330035";
    /** 查询高端基金份额迁移校验 */
    public static final String QUERY_SHARE_TRANSFER_VALIDATE = "Z330036";
    /** 高端柜台基金份额合并下单 */
    public static final String HIGH_SHARE_MERGE_COUNTER = "Z330037";
    /** 高端柜台基金份额迁移下单 */
    public static final String HIGH_SHARE_TRANSFER_COUNTER = "Z330038";
    /** 查询高端产品柜台持仓列表接口 */
    public static final String HIGH_FUND_QUERY_ACCT_BALANCE_COUNTER = "Z330039";
    /** 高端发送鉴权信息 */
    public static final String HIGH_SEND_AUTH_MSG = "Z330040";
    /** 查询订单信息（ES-CENTER用） */
    public static final String QUERY_HIGH_DEAL_ORDER_ES = "Z330041";
    /** 查询客户复购协议 */
    public static final String HIGH_QUERY_CUST_REPURCHASE_PROTOCOL = "Z330041";
    /** 修改客户复购协议 */
    public static final String HIGH_MODIFY_REPURCHASE_PROTOCOL = "Z330042";

    /** 查询高端基金非交易过户校验 */
    public static final String QUERY_NOTRADE_OVERACCOUNT_VALIDATE = "Z330043";
    /** 高端基金非交易过户 */
    public static final String HIGH_NOTRADE_OVERACCOUNT_COUNTER = "Z330044";
    /** 查询客户待补签协议列表 */
    public static final String QUERY_SUP_SIGN_AGREEMENT_LIST = "Z330045";
    /** 签署待补签协议 */
    public static final String SUBMIT_SUP_SIGN_AGREEMENT = "Z330046";
    /** 查询电子签名文件路径接口 */
    public static final String HIGH_FUND_QUERY_ESIGNATURE_FILE_PATH = "Z330047";
    /** 高端机构基金认申购 */
    public static final String HIGH_SUBSORPUR_INST = "Z330048";
    /** 高端机构基金赎回 */
    public static final String HIGH_REDEEM_INST = "Z330049";
    /** crm查询份额明细 */
    public static final String HIGH_FUND_QUERY_BALANCE_VOL_DTL_FOR_CRM = "Z330050";
    /** 订单回款信息修改 */
    public static final String HIGH_DEAL_ORDER_REFUND_MODIFY = "Z330051";
    /** 齐欣保险回调接口 */
    public static final String SALE_QIXIN_CALLBACK = "Z330052";
    /** 查询保险订单列表 */
    public static final String QUERY_SALE_ORDER_LIST = "Z330053";
    /** 定投第一步，查询客户银行卡信息和持仓信息 */
    public static final String QUERY_INV_PLAN_CUST_INFO_FOR_CRM = "Z330054";
    /** 定投第二步，定投校验 */
    public static final String INV_PLAN_VALIDATE_FOR_CRM = "Z330055";
    /** 定投第二步，生成定投计划 */
    public static final String GENERATE_INV_PLAN_FOR_CRM = "Z330056";
    /** 执行定投计划 */
    public static final String EXECUTE_INV_PLAN = "Z330057";
    /** 查询定投信息 */
    public static final String QUERY_HIGH_FUND_INV_PLAN = "Z330058";
    /** 查询定投明细信息 */
    public static final String QUERY_HIGH_FUND_INV_PLAN_DTL = "Z330059";
    /** 查询管理人可购买的产品 */
    public static final String QUERY_BUY_FUND_STATUS_FOR_DS = "Z330060";
    /** 查询资金到账提醒数据 add 20230110 */
    public static final String HIGH_FIN_RECEIPT = "Z330061";
    /** 查询客户持有基金风险等级 */
    public static final String QUERY_CUST_FUND_RISK_LEVEL = "Z330062";
    /** 查询所有产品的购买状态 */
    public static final String QUERY_ALL_FUND_BUY_STATUS = "Z330063";
    /** 查询所有产品的赎回状态 */
    public static final String QUERY_ALL_FUND_REDEEM_STATUS = "Z330064";

    /** 查询用户股权订单 */
    public static final String QUERY_ACCT_OWNERSHIP = "Z330065";

    /** 查询高端用户账户持仓基本信息信息 */
    public static final String QUERY_ACCT_BALANCE_BASE_INFO = "Z330066";

    /** 查询高端持仓详情额外补充信息 */
    public static final String QUERY_BALANCE_OTHER_INFO = "Z330067";

    /** 查询好臻下单所需信息 */
    public static final String QUERY_HZ_BUY_ORDER_INFO = "Z330068";
    /** 反洗钱强控校验 */
    public static final String VALID_CUSTOMER_INFO_STRONGLY = "Z330069";
    /** 查询高端用户基础信息 */
    public static final String QUERY_ACCT_STRATEGY = "Z330070";
    /** 在途订单查询请求 */
    public static final String QUERY_ACCT_ON_WAY_ORDER = "Z330071";
    /** 查询好臻认缴金额相关信息接口 */
    public static final String QUERY_HZ_SUBSCRIBE_AMT_INFO = "Z330072";
    /** 查询表单模版 */
    public static final String QUERY_FORM_TEMPLATE = "Z330073";
    /** 用户提交表单 */
    public static final String SUBMIT_FORM = "Z330074";
    /** 查询用户认缴信息接口 */
    public static final String QUERY_SUBSAMT_INFO = "Z330075";
    /** 查询用户认缴信息接口 */

    /** 查询产品费用信息接口 */
    public static final String QUERY_FEE_INFO = "Z330076";
    /** 查询订单信息口 */
    public static final String QUERY_DEAL_INFO = "Z330077";
    /** 查询订单信息口 */
    public static final String QUERY_HIGH_INTRANSIT = "Z330078";
    /** 查询订单信息口 */
    public static final String QUERY_HIGH_CUST_BALANCE_DETAIL = "Z330079";
    /** 查询用户产品认缴信息接口 */
    public static final String QUERY_CUSTOMER_FUND_SUBS_AMT = "Z330080";
    /** 查询用户持仓基金接口 */
    public static final String QUERY_CUSTOMER_BALANCE_FUND = "Z330081";
    /** 查询用户持仓基金接口 */
    public static final String QUERY_CUSTOMER_REDEEM_APPOINT_INFO = "Z330082";
    /** 查询持仓子账单明细中,有持仓的产品列表 */
    public static final String QUERY_ALL_SUB_CUSTBOOKS_FUND_INFO = "Z330083";
    /** 查询让利型基金升级记录 */
    public static final String QUERY_PROFIT_FUND_UPGRADE_RECORD = "Z330077";
    /** 查询持有该产品的用户列表 */
    public static final String QUERY_BALANCE_TX_ACCT_NO_BY_FUND = "Z330084";

    /** 查询基金确认基金与份额 */
    public static final String QUERY_FUND_ACK_VOL_AMT = "Z330085";
    /** 查询高端产品持仓接口(优化版) */
    public static final String HIGH_FUND_QUERY_ACCT_BALANCE_OPTIMIZED = "Z330086";
}
