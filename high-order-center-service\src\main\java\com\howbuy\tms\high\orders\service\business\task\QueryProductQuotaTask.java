/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.cache.service.highquota.HighProductQuotaService;
import com.howbuy.tms.cache.service.highquota.HighQuotaBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductLockInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductLockWhithListBean;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.CmBlacklistDirectPo;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse.QuotaBean;
import com.howbuy.tms.high.orders.service.repository.CmBlacklistDirectRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询高端产品额度任务类
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月12日 下午5:52:16
 * @since JDK 1.7
 */
public class QueryProductQuotaTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryProductQuotaTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;

    private QuotaBean quotaBean;
    
    private HighProductQuotaService highProductQuotaService;
    
    private CmBlacklistDirectRepository cmBlacklistDirectRepository;
    
    private String hbOneNo;

    private QueryCustInfoResult custInfo;
    
    private String dataSourceKey;

    private CountDownLatch latch;

    public QueryProductQuotaTask(QueryHighProductOuterService queryHighProductOuterService, String hbOneNo, QuotaBean quotaBean, String dataSourceKey,
            HighProductQuotaService highProductQuotaService, CmBlacklistDirectRepository cmBlacklistDirectRepository, QueryCustInfoResult custInfo,
                                 CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.hbOneNo = hbOneNo;
        this.quotaBean = quotaBean;
        this.dataSourceKey = dataSourceKey;
        this.highProductQuotaService = highProductQuotaService;
        this.cmBlacklistDirectRepository = cmBlacklistDirectRepository;
        this.custInfo = custInfo;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            String productCode = quotaBean.getProductCode();
            
            // 查询产品总额度/总人数
            HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
            if (highProductBaseInfoBean != null) {
                quotaBean.setTotalAmt(highProductBaseInfoBean.getIpoAmountLimit());
                quotaBean.setTotalPlaces(highProductBaseInfoBean.getIpoPeopleLimit());
            }
            
            // 已消耗的额度
            HighQuotaBean highQuotaBean = highProductQuotaService.getQuotaInfo(productCode);
            if (highQuotaBean != null) {
                quotaBean.setCostTotalAmt(highQuotaBean.getLockUsedAmount().add(highQuotaBean.getUnLockUsedAmount()));
                quotaBean.setCostTotalPlaces(Long.valueOf(highQuotaBean.getLockUsedCount()) + highQuotaBean.getUnLockUsedCount());
                quotaBean.setLockExitCount(Long.valueOf(highQuotaBean.getLockExitCount()));
            }

            BigDecimal leftAmt = getLeftAmt(productCode, highQuotaBean);
            quotaBean.setLeftAmt(leftAmt);// 剩余金额

            // 直销黑名单校验
            CmBlacklistDirectPo cmBlacklistDirectPo = cmBlacklistDirectRepository.selectByHbOneNoAndFundCode(hbOneNo, productCode);
            if (cmBlacklistDirectPo == null) {
                quotaBean.setInDirectBlackList("0");
            } else {
                quotaBean.setInDirectBlackList("1");
            }
        }catch(RuntimeException ex){
            logger.error("QueryProductQuotaTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

    private BigDecimal getLeftAmt(String productCode, HighQuotaBean highQuotaBean) {
        HighProductLockInfoBean highProductLockInfoBean = queryHighProductOuterService.getHighProductLockInfo(productCode);
        BigDecimal lockAmount = BigDecimal.ZERO; // 锁定金额
        if(highProductLockInfoBean != null){
            lockAmount = highProductLockInfoBean.getLockAmount() == null ? BigDecimal.ZERO : highProductLockInfoBean.getLockAmount();
        }
        BigDecimal totalLeftAmt = BigDecimal.ZERO;
        totalLeftAmt = MathUtils.subtract(quotaBean.getTotalAmt(), quotaBean.getCostTotalAmt());
        BigDecimal leftAmt = BigDecimal.ZERO;
        if(StringUtils.isNotEmpty(hbOneNo)){
            HighProductLockWhithListBean lockWhithBean = queryHighProductOuterService.getProductWhiteByHboneNoAndFund(hbOneNo, quotaBean.getProductCode());
            if(lockWhithBean != null){
                leftAmt = MathUtils.subtract(lockAmount, highQuotaBean.getLockUsedAmount());
            }else {
                leftAmt =MathUtils.subtract(MathUtils.subtract(quotaBean.getTotalAmt(), lockAmount), highQuotaBean.getUnLockUsedAmount()) ;
            }

            return totalLeftAmt.compareTo(leftAmt) < 0 ? totalLeftAmt : leftAmt;

        }else {
            return totalLeftAmt;
        }
    }

}

