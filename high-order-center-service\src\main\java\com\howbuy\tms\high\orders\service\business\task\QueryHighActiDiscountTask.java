/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductActiDiscountListBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductActiDiscountListBean.HighProductActiDiscountBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.context.QueryHighActiDiscountContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * 
 * @description:(查询活动折扣)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月25日 上午10:57:40
 * @since JDK 1.6
 */
public class QueryHighActiDiscountTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryHighActiDiscountTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;

    private QueryHighActiDiscountContext queryHighActiDiscountContext;
    private HighProductActiDiscountListBean  highProductActiDiscountListBean;
    private CountDownLatch latch;

    public QueryHighActiDiscountTask(QueryHighProductOuterService queryHighProductOuterService,  HighProductActiDiscountListBean  highProductActiDiscountListBean, QueryHighActiDiscountContext queryHighActiDiscountContext, CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.queryHighActiDiscountContext = queryHighActiDiscountContext;
        this.highProductActiDiscountListBean = highProductActiDiscountListBean;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            HighProductActiDiscountListBean bean = queryHighProductOuterService.queryHighActiDiscount(queryHighActiDiscountContext);
            if (bean == null) {
                return null;
            }
            
            BeanUtils.copyProperties(bean, highProductActiDiscountListBean);
            if(CollectionUtils.isNotEmpty(bean.getDiscountList())){
                List<HighProductActiDiscountBean> rstList = new ArrayList<HighProductActiDiscountBean>();
                HighProductActiDiscountBean highProductActiDiscount = null;
                for(HighProductActiDiscountBean highProductActiDiscountBean : bean.getDiscountList()){
                    highProductActiDiscount = new HighProductActiDiscountBean();
                    BeanUtils.copyProperties(highProductActiDiscountBean, highProductActiDiscountBean);
                    rstList.add(highProductActiDiscount);
                }
                highProductActiDiscountListBean.setDiscountList(rstList);
                
            }
        }catch(RuntimeException ex){
            logger.error("QueryHighActiDiscountTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

