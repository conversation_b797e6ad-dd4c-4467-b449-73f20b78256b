/**
 * Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryacctbalanceoptimized;

import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 查询客户持仓响应(优化版)
 * @author: hongdong.xie
 * @date: 2025/8/15 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryAcctBalanceOptimizedResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 分销机构代码
     */
    private String disCode;

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    /**
     * 总市值
     */
    private BigDecimal totalMarketValue;

    /**
     * 在途总金额
     */
    private BigDecimal totalUnconfirmedAmt;

    /**
     * 待确认笔数
     */
    private Integer totalUnconfirmedNum;

    /**
     * 赎回待确认笔数
     */
    private Integer redeemUnconfirmedNum;

    /**
     * 当前总收益
     */
    private BigDecimal totalCurrentAsset;

    /**
     * 总收益计算状态: 0-计算中;1-计算成功
     */
    private String totalIncomCalStat;

    /**
     * 总回款
     */
    private BigDecimal totalCashCollection;

    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct = YesOrNoEnum.NO.getCode();

    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct = YesOrNoEnum.NO.getCode();

    /**
     * 持仓明细列表
     */
    private List<OptimizedBalanceBean> balanceList = new ArrayList<>();

    /**
     * 在途产品列表
     */
    private List<UnconfirmeProduct> unconfirmeProducts = new ArrayList<>();

    /**
     * 优化后的持仓信息Bean，只保留核心字段
     */
    @Getter
    @Setter
    public static class OptimizedBalanceBean implements Serializable, Comparable<OptimizedBalanceBean> {
        private static final long serialVersionUID = 1L;

        /**
         * 分销代码
         */
        private String disCode;

        /**
         * 分销代码列表
         */
        private List<String> disCodeList;

        /**
         * 产品代码
         */
        private String productCode;

        /**
         * 子产品代码
         */
        private String subProductCode;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 产品类型
         */
        private String productType;

        /**
         * 产品子类型(好买产品线)
         */
        private String productSubType;

        /**
         * 总份额
         */
        private BigDecimal balanceVol;

        /**
         * 待确认份额
         */
        private BigDecimal unconfirmedVol;

        /**
         * 待确认金额
         */
        private BigDecimal unconfirmedAmt;

        /**
         * 币种
         */
        private String currency;

        /**
         * 净值
         */
        private BigDecimal nav;

        /**
         * 净值日期
         */
        private String navDt;

        /**
         * 净值分红标识 0-否，1-是
         */
        private String navDivFlag = "0";

        /**
         * 实缴金额
         */
        private BigDecimal paidTotalAmt;

        /**
         * 市值
         */
        private BigDecimal marketValue;

        /**
         * 当前收益
         */
        private BigDecimal currentIncome;

        /**
         * 当前收益(人民币)
         */
        private BigDecimal currentIncomeRmb;

        /**
         * 累计收益
         */
        private BigDecimal accumIncome;

        /**
         * 累计收益(人民币)
         */
        private BigDecimal accumIncomeRmb;

        /**
         * 累计成本
         */
        private BigDecimal accumCost;

        /**
         * 累计成本(人民币)
         */
        private BigDecimal accumCostRmb;

        /**
         * 累计回款
         */
        private BigDecimal accumCollection;

        /**
         * 累计回款(人民币)
         */
        private BigDecimal accumCollectionRmb;

        /**
         * 回款金额
         */
        private BigDecimal cashCollection;

        /**
         * 收益率
         */
        private BigDecimal yieldRate;

        /**
         * 收益计算状态
         */
        private String incomeCalStat;

        /**
         * 规模类型 0-代销，1-直销
         */
        private String scaleType;

        /**
         * 好买香港代销标识
         */
        private String hkSaleFlag;

        /**
         * 成立日期
         */
        private String establishDt;

        /**
         * 到期日期
         */
        private String dueDate;

        /**
         * 投资期限
         */
        private String investmentHorizon;

        /**
         * 危机标识
         */
        private String crisisFlag;

        /**
         * 异常标识
         */
        private String abnormalFlag;

        /**
         * 分次call标识
         */
        private String fractionateCallFlag;

        /**
         * 分期成立标识
         */
        private String stageEstablishFlag;

        /**
         * 千禧标识
         */
        private String qianXiFlag;

        /**
         * 待投金额（人民币）
         */
        private BigDecimal unPaidInAmt;

        /**
         * 待投金额（当前币种）
         */
        private BigDecimal currencyUnPaidInAmt;

        /**
         * 收益日期
         */
        private String incomeDt;

        /**
         * 净值披露方式
         */
        private String navDisclosureType;

        /**
         * 实缴金额
         */
        private BigDecimal paidInAmt;

        /**
         * 净购买金额(股权产品)
         */
        private BigDecimal netBuyAmount;

        /**
         * 当前币种净购买金额(股权产品)
         */
        private BigDecimal currencyNetBuyAmount;

        /**
         * 持仓成本
         */
        private BigDecimal balanceCost;

        /**
         * 持仓成本(当前币种)
         */
        private BigDecimal balanceCostCurrency;

        /**
         * 当前收益(当前币种)
         */
        private BigDecimal currentAssetCurrency;

        /**
         * 日收益
         */
        private BigDecimal dailyAsset;

        /**
         * 日收益(当前币种)
         */
        private BigDecimal dailyAssetCurrency;

        /**
         * 累计已实现收益
         */
        private BigDecimal accumRealizedIncome;

        /**
         * 累计已实现收益(人民币)
         */
        private BigDecimal accumRealizedIncomeRmb;

        /**
         * 浮动收益
         */
        private BigDecimal balanceFloatIncome;

        /**
         * 浮动收益(人民币)
         */
        private BigDecimal balanceFloatIncomeRmb;

        /**
         * 浮动收益率
         */
        private BigDecimal balanceFloatIncomeRate;

        /**
         * 持仓金额
         */
        private BigDecimal balanceAmt;

        /**
         * 持仓金额(人民币)
         */
        private BigDecimal balanceAmtRmb;

        /**
         * 持仓金额去费
         */
        private BigDecimal balanceAmtExFee;

        /**
         * 持仓金额去费(人民币)
         */
        private BigDecimal balanceAmtExFeeRmb;

        /**
         * 单位持仓成本去费
         */
        private BigDecimal unitBalanceCostExFee;

        /**
         * 单位持仓成本去费(人民币)
         */
        private BigDecimal unitBalanceCostExFeeRmb;

        /**
         * 累计成本(新)
         */
        private BigDecimal accumCostNew;

        /**
         * 累计成本(新)(人民币)
         */
        private BigDecimal accumCostRmbNew;

        /**
         * 当前收益(新)
         */
        private BigDecimal balanceIncomeNew;

        /**
         * 当前收益(新)(人民币)
         */
        private BigDecimal balanceIncomeNewRmb;

        /**
         * 累计收益(新)
         */
        private BigDecimal accumIncomeNew;

        /**
         * 累计收益(新)(人民币)
         */
        private BigDecimal accumIncomeNewRmb;

        /**
         * 股权产品转让标识
         */
        private String ownershipTransferIdentity;

        /**
         * 资产更新日期
         */
        private String assetUpdateDate;

        /**
         * 七日年化收益率
         */
        private BigDecimal yieldIncome;

        /**
         * 七日年化收益率日期
         */
        private String yieldIncomeDt;

        /**
         * 万份收益
         */
        private BigDecimal copiesIncome;

        /**
         * 日收益率
         */
        private BigDecimal dayAssetRate;

        /**
         * 日收益增长率
         */
        private BigDecimal dayIncomeGrowthRate;

        /**
         * 累计收益率
         */
        private BigDecimal accumYieldRate;

        /**
         * 市值去费(当前币种)
         */
        private BigDecimal currencyMarketValueExFee;

        /**
         * 市值去费
         */
        private BigDecimal marketValueExFee;

        /**
         * 管理费
         */
        private BigDecimal receivManageFee;

        /**
         * 业绩报酬
         */
        private BigDecimal receivPreformFee;

        /**
         * 起息日
         */
        private String valueDate;

        /**
         * 基准类型
         */
        private String benchmarkType;

        /**
         * 产品销售类型
         */
        private String productSaleType;

        /**
         * NA产品收费类型
         */
        private String naProductFeeType;

        /**
         * 是否海外产品
         */
        private String hwSaleFlag;

        /**
         * 标准固收标识
         */
        private String standardFixedIncomeFlag;

        /**
         * 存续期限描述
         */
        private String fundCXQXStr;

        /**
         * 是否拆单产品
         */
        private String stageFlag;

        /**
         * 是否持仓
         */
        public boolean isBalance() {
            if ("1".equals(scaleType)) { // 直销
                if ("1".equals(hkSaleFlag)) { // 海外
                    return balanceVol != null && balanceVol.compareTo(BigDecimal.ZERO) > 0;
                } else {
                    return balanceVol != null && balanceVol.compareTo(BigDecimal.ONE) > 0;
                }
            } else { // 代销
                return balanceVol != null && balanceVol.compareTo(BigDecimal.ZERO) > 0;
            }
        }

        @Override
        public int compareTo(OptimizedBalanceBean bean) {
            return this.productCode.compareTo(bean.productCode);
        }
    }

    /**
     * 在途产品信息
     */
    @Getter
    @Setter
    public static class UnconfirmeProduct implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 产品代码
         */
        private String fundCode;

        /**
         * 产品类型
         */
        private String productType;

        /**
         * 产品子类型
         */
        private String productSubType;

        /**
         * 待确认金额(人民币)
         */
        private BigDecimal unconfirmedAmt;

        /**
         * 好买香港代销标识: 0-否; 1-是
         */
        private String hkSaleFlag;

        /**
         * 销售渠道
         */
        private String disCode;
    }
}
