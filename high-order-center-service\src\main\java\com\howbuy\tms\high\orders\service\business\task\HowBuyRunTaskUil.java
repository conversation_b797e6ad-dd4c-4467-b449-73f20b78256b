package com.howbuy.tms.high.orders.service.business.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Description:基础任务执行类
 * @Author: yun.lu
 * Date: 2024/7/5 11:19
 */
@Component
@Slf4j
public class HowBuyRunTaskUil {
    @Autowired
    private ThreadPoolTaskExecutor threadPoolExecutor;

    /**
     * 批量执行异步任务
     *
     * @param taskList 任务列表
     * @param <E>      任务类型
     */
    public <E extends HowbuyBaseTask> void runTask(List<E> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(taskList.size());
        for (HowbuyBaseTask howbuyBaseTask : taskList) {
            howbuyBaseTask.setLatch(latch);
            threadPoolExecutor.execute(howbuyBaseTask);
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch await出现异常,现成终止");
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 执行单个异步任务
     *
     * @param task 异步任务
     * @param <E>  任务类型
     */
    public <E extends HowbuyBaseTask> void runTask(E task) {
        if (task == null) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(1);
        task.setLatch(latch);
        threadPoolExecutor.execute(task);
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch await出现异常,现成终止");
            Thread.currentThread().interrupt();
        }
    }
}
