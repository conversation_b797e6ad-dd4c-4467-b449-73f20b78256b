/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.business.busiprocess.task.QueryCanBuyFundsForDsTask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.QueryCanBuyFundsForDsRequest;
import com.howbuy.tms.high.orders.facade.search.querycanbuyfundsfords.bean.CanBuyFunds;
import com.howbuy.tms.high.orders.service.business.task.QueryCanBuyFundsForDsTask;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

import static org.mockito.Mockito.times;
import static org.powermock.api.mockito.PowerMockito.verifyPrivate;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2022/6/17 13:05
 * @since JDK 1.8
 */
@RunWith(MockitoJUnitRunner.class)
@PowerMockIgnore({"javax.security.*","javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
@PrepareForTest({QueryCanBuyFundsForDsTask.class})
public class CallTaskTestM {

    @InjectMocks
    private QueryCanBuyFundsForDsTask queryCanBuyFundsForDsTask;
    @Mock
    private QueryHighProductOuterService queryHighProductOuterService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        queryCanBuyFundsForDsTask = PowerMockito.spy(queryCanBuyFundsForDsTask);
    }

    /**
     * @description:(ta状态不正常)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void taStatError() throws Exception {
        execute("taStatError.json");
    }

    /**
     * @description:(交易渠道不正确)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void txChannelError() throws Exception {
        execute("txChannelError.json");
    }

    /**
     * @description:(用户类型错误)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void userTypeError() throws Exception {
        execute("userTypeError.json");
    }

    /**
     * @description:(产品类型错误)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void productTypeError() throws Exception {
        execute("productTypeError.json");
    }

    /**
     * @description:(分销错误)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void disCodeError() throws Exception {
        execute("disCodeError.json");
    }

    /**
     * @description:(预约状态错误)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void tradeStatusError() throws Exception {
        execute("tradeStatusError.json");
    }

    /**
     * @description:(成功)
     * @param
     * @return void
     * @author: haiguang.chen
     * @date: 2022/6/17 15:14
     * @since JDK 1.8
     */
    @Test
    public void success() throws Exception {
        execute("success.json");
    }

    public void execute(String fileName) throws Exception {
        String jsonStr = readFile(fileName);
        JSONObject obj = JSON.parseObject(jsonStr);
        HighProductInfoModel model = JSONObject.parseObject(String.valueOf(obj.getJSONObject("HighProductInfoModel")), HighProductInfoModel.class);
        List<HighProductInfoModel> highProductInfoModelList = new ArrayList<>();
        highProductInfoModelList.add(model);
        queryCanBuyFundsForDsTask = PowerMockito.spy(queryCanBuyFundsForDsTask);

        QueryCanBuyFundsForDsRequest request = JSONObject.parseObject(String.valueOf(obj.getJSONObject("QueryCanBuyFundsForDsRequest")), QueryCanBuyFundsForDsRequest.class);
        String taTradeDt = (String)obj.getJSONObject("Other").get("taTradeDt");
        String ordersKey = (String)obj.getJSONObject("Other").get("ordersKey");
        boolean validIsSupCounterOrWebBool = (boolean)obj.getJSONObject("Other").get("validIsSupCounterOrWebBool");
        boolean validIsSupBuyInvstTypeBool = (boolean)obj.getJSONObject("Other").get("validIsSupBuyInvstTypeBool");
        boolean validateProductTradeStatusBool = (boolean)obj.getJSONObject("Other").get("validateProductTradeStatusBool");
        Integer validIsSupCounterOrWebNum = (Integer)obj.getJSONObject("Result").get("validIsSupCounterOrWebNum");
        Integer validIsSupBuyInvstTypeNum = (Integer)obj.getJSONObject("Result").get("validIsSupBuyInvstTypeNum");
        Integer validateProductTradeStatusNum = (Integer)obj.getJSONObject("Result").get("validateProductTradeStatusNum");
        CountDownLatch latch = new CountDownLatch(highProductInfoModelList.size());;
        List<CanBuyFunds.FundInfo> fundInfos = new ArrayList<>();

        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"queryHighProductOuterService").set(queryCanBuyFundsForDsTask,queryHighProductOuterService);
        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"highProductInfoModelList").set(queryCanBuyFundsForDsTask,highProductInfoModelList);
        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"taTradeDt").set(queryCanBuyFundsForDsTask,taTradeDt);
        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"request").set(queryCanBuyFundsForDsTask,request);
        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"latch").set(queryCanBuyFundsForDsTask,latch);
        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"ordersKey").set(queryCanBuyFundsForDsTask,ordersKey);
        MemberModifier.field(QueryCanBuyFundsForDsTask.class,"fundInfos").set(queryCanBuyFundsForDsTask,fundInfos);

        PowerMockito.doReturn(validIsSupCounterOrWebBool).when(queryCanBuyFundsForDsTask,"validIsSupCounterOrWeb",Mockito.any(), Mockito.any());
        PowerMockito.doReturn(validIsSupBuyInvstTypeBool).when(queryCanBuyFundsForDsTask,"validIsSupBuyInvstType",Mockito.any(), Mockito.anyString());
        PowerMockito.doReturn(validateProductTradeStatusBool).when(queryCanBuyFundsForDsTask,"validateProductTradeStatus",Mockito.anyObject(), Mockito.anyObject(), Mockito.anyString());

        queryCanBuyFundsForDsTask.call();

        verifyPrivate(queryCanBuyFundsForDsTask, times(validIsSupCounterOrWebNum)).invoke("validIsSupCounterOrWeb",Mockito.any(),  Mockito.any());
        verifyPrivate(queryCanBuyFundsForDsTask, times(validIsSupBuyInvstTypeNum)).invoke("validIsSupBuyInvstType",Mockito.any(), Mockito.any());
        verifyPrivate(queryCanBuyFundsForDsTask, times(validateProductTradeStatusNum)).invoke("validateProductTradeStatus",Mockito.any(), Mockito.any(), Mockito.anyString());

    }

    private static String readFile(String fileName) throws Exception {
        String path = "src/test/java/com/howbuy/tms/high/orders/service/business/busiprocess/task/QueryCanBuyFundsForDsTask";
        InputStream is = new FileInputStream(path+ File.separator+fileName);
        BufferedReader in = new BufferedReader(new InputStreamReader(is));
        StringBuilder buffer = new StringBuilder();
        String line = "";
        while ((line = in.readLine()) != null){
            buffer.append(line);
        }
        return buffer.toString();
    }


}