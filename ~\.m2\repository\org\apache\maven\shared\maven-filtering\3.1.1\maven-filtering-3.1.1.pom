<?xml version='1.0' encoding='UTF-8'?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements. See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership. The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.shared</groupId>
    <artifactId>maven-shared-components</artifactId>
    <version>30</version>
    <relativePath>../../pom/maven/maven-shared-components/pom.xml</relativePath>
  </parent>

  <artifactId>maven-filtering</artifactId>
  <version>3.1.1</version>

  <name>Apache Maven Filtering</name>
  <description>A component to assist in filtering of resource files with properties from a Maven project.</description>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/shared/tags/maven-filtering-3.1.1</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/shared/tags/maven-filtering-3.1.1</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/shared/tags/maven-filtering-3.1.1</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/MSHARED/component/12326444</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <mavenVersion>3.0</mavenVersion>
  </properties>

  <contributors>
    <contributor>
      <name>Graham Leggett</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-utils</artifactId>
      <version>3.0.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0.24</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-interpolation</artifactId>
      <version>1.22</version>
    </dependency>
    <dependency>
      <groupId>org.sonatype.plexus</groupId>
      <artifactId>plexus-build-api</artifactId>
      <version>0.0.7</version>
    </dependency>

    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.plexus</groupId>
      <artifactId>plexus-build-api</artifactId>
      <!-- FIXME: Upgrade from 0.0.4 to 0.0.7 will fail some tests -->
      <version>0.0.4</version>
      <scope>test</scope>
      <classifier>tests</classifier>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>1.9.5</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
    
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>1.7.1</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-changes-plugin</artifactId>
            <configuration>
              <columnNames>Type,Key,Summary,Assignee,Status,Resolution,Created</columnNames>
              <maxEntries>200</maxEntries>
              <onlyCurrentVersion>true</onlyCurrentVersion>
              <sortColumnNames>Key</sortColumnNames>
              <versionPrefix>maven-filtering-</versionPrefix>
              <fixVersionIds>12335751</fixVersionIds>
              <onlyCurrentVersion>true</onlyCurrentVersion>
            </configuration>
            <reportSets>
              <reportSet>
                <reports>
                  <report>jira-report</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
