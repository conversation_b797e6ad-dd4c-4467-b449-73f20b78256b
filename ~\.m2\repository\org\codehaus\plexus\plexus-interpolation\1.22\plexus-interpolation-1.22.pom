<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus-components</artifactId>
    <version>1.3.1</version>
  </parent>

  <artifactId>plexus-interpolation</artifactId>
  <version>1.22</version>

  <name>Plexus Interpolation API</name>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-interpolation.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-interpolation.git</developerConnection>
    <url>http://github.com/sonatype/plexus-interpolation</url>
    <tag>plexus-interpolation-1.22</tag>
  </scm>

<build>
<plugins>  
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.1</version>
        <configuration>
          <!-- olamy: exclude files with strange names as failed here on osx -->
          <checkModificationExcludes>
            <checkModificationExclude>**/src/test/resources/utf8/**</checkModificationExclude>
          </checkModificationExcludes>
        </configuration>
      </plugin>
</plugins>
</build>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
      
</project>
