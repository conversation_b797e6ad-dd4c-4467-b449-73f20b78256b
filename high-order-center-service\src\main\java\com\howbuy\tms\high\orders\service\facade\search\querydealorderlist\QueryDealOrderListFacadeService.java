/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.querydealorderlist;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.AdvanceFlagEnum;
import com.howbuy.tms.common.enums.database.OrderStatusEnum;
import com.howbuy.tms.common.enums.database.PayStatusEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.outerservice.simu.productinfo.QuerySimuProductInfoOuterService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import com.howbuy.tms.high.orders.dao.vo.QueryDealOrderListCondition;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse.DealOrderBean;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.business.querymeragesubmit.QueryMergeSubmitOrderService;
import com.howbuy.tms.high.orders.service.business.task.DealOrderProcessTask;
import com.howbuy.tms.high.orders.service.business.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.business.task.QueryProductInfoTask;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.HighRedeemSplitDtlRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:(查询交易记录列表)
 * @reason:
 * @date 2017年7月12日 下午5:09:12
 * @since JDK 1.7
 */
@DubboService
@Service("queryDealOrderListFacade")
public class QueryDealOrderListFacadeService extends AbstractBusiProcess implements QueryDealOrderListFacade {
    private static final Logger logger = LogManager.getLogger(QueryDealOrderListFacadeService.class);

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private QueryMergeSubmitOrderService queryMergeSubmitOrderService;


    @Autowired
    private QuerySimuProductInfoOuterService querySimuProductInfoOuterService;

    @Autowired
    private HighRedeemSplitDtlRepository highRedeemSplitDtlRepository;

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade.execute(QueryDealOrderListRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryDealOrderListFacadeService
     * @apiName execute
     * @apiDescription 查询交易记录列表
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} productName 产品名称
     * @apiParam (请求参数) {Number} appBeginDtm 交易开始时间
     * @apiParam (请求参数) {Number} appEndDtm 交易结束时间
     * @apiParam (请求参数) {String} cpAcctNo 资金账号
     * @apiParam (请求参数) {Array} mBusiCodeArr 中台业务码
     * @apiParam (请求参数) {Array} orderStatusArr 订单状态
     * @apiParam (请求参数) {String} fundNameOrCode 基金代码或名称
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} dealNo 订单号
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=L&mBusiCodeArr=qff&orderStatusArr=yI3ZQB&pageSize=6862&disCode=7m&txChannel=Ro2BU7JxF&productName=qu&disCodeList=TD6if&pageNo=3688&operIp=StOWdHC&txAcctNo=j3JHpk7y&cpAcctNo=KXrTLfI5E&appDt=0EIk&notFilterHkFund=g2QgCizpu&hbOneNo=w&fundNameOrCode=ha&appEndDtm=603367459993&dealNo=RmMwJOeOh&appBeginDtm=*************&appTm=7a&productCode=Fj&subOutletCode=MJsJmU3&dataTrack=q4R1weF&txCode=Qe&outletCode=0MhD1kG0
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {Array} dealOrderList 高端订单列表
     * @apiSuccess (响应结果) {String} dealOrderList.dealNo 客户订单号
     * @apiSuccess (响应结果) {String} dealOrderList.disCode 分销代码
     * @apiSuccess (响应结果) {String} dealOrderList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} dealOrderList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} dealOrderList.bankAcct 银行账号
     * @apiSuccess (响应结果) {String} dealOrderList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} dealOrderList.paymentType 银行代码
     * @apiSuccess (响应结果) {String} dealOrderList.productName 产品名称
     * @apiSuccess (响应结果) {String} dealOrderList.productAttr 产品简称
     * @apiSuccess (响应结果) {String} dealOrderList.productCode 产品代码
     * @apiSuccess (响应结果) {Number} dealOrderList.appAmt 申请金额
     * @apiSuccess (响应结果) {Number} dealOrderList.appVol 申请份额
     * @apiSuccess (响应结果) {Number} dealOrderList.appDtm 申请日期时间
     * @apiSuccess (响应结果) {String} dealOrderList.payStatus 付款状态
     * @apiSuccess (响应结果) {String} dealOrderList.orderStatus 订单状态
     * @apiSuccess (响应结果) {String} dealOrderList.taTradeDt TA交易日期
     * @apiSuccess (响应结果) {Number} dealOrderList.fee 手续费
     * @apiSuccess (响应结果) {String} dealOrderList.mBusiCode 中台业务码
     * @apiSuccess (响应结果) {String} dealOrderList.redeemDirection 赎回去向
     * @apiSuccess (响应结果) {String} dealOrderList.scaleType 销售类型: 1-直销; 2-代销
     * @apiSuccess (响应结果) {String} dealOrderList.divMode 分红方式
     * @apiSuccess (响应结果) {Number} dealOrderList.ackVol 确认份额
     * @apiSuccess (响应结果) {Number} dealOrderList.ackAmt 确认金额
     * @apiSuccess (响应结果) {String} dealOrderList.ackDt 确认日期
     * @apiSuccess (响应结果) {String} dealOrderList.memo 备注字段
     * @apiSuccess (响应结果) {Number} dealOrderList.nav 净值
     * @apiSuccess (响应结果) {String} dealOrderList.advanceFlag 递延标识
     * @apiSuccess (响应结果) {String} dealOrderList.productType 产品类型
     * @apiSuccess (响应结果) {String} dealOrderList.submitTaDt 上报TA日期
     * @apiSuccess (响应结果) {String} dealOrderList.productSubType 产品子类型
     * @apiSuccess (响应结果) {String} dealOrderList.isVolTansfer 是否份额结转 1是，0否
     * @apiSuccess (响应结果) {String} dealOrderList.mergeSubmitFlag 合并上报标识 1-合并上报
     * @apiSuccess (响应结果) {String} dealOrderList.currency 币种
     * @apiSuccess (响应结果) {String} dealOrderList.highFundInvPlanFlag 是否私募定投 0-不是 1-是
     * @apiSuccess (响应结果) {String} dealOrderList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} dealOrderList.sffqcl 是否分期成立（证券类有此标识）
     * @apiSuccess (响应结果) {String} dealOrderList.subProductCode 子基金代码
     * @apiSuccess (响应结果) {String} dealOrderList.joinDt 参与日期
     * @apiSuccess (响应结果) {String} dealOrderList.continuanceFlag 顺延标识，0-否、1-是
     * @apiSuccess (响应结果) {String} dealOrderList.stageFlag 拆单标识（淡水泉分期成立），0-否、1-是
     * @apiSuccess (响应结果) {String} dealOrderList.txAckFlag
     * @apiSuccess (响应结果) {Number} dealOrderList.transferPrice 转让价格
     * @apiSuccess (响应结果) {String} dealOrderList.isNoTradeTransfer 是否非交易转让,0:不是,1:是
     * @apiSuccess (响应结果) {Array} dealOrderList.subDealOrderBeans
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.dealNo 客户订单号
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.disCode 分销代码
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.bankAcct 银行账号
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.bankCode 银行代码
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.paymentType 银行代码
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.productName 产品名称
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.productAttr 产品简称
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.productCode 产品代码
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.subProductCode 子基金代码
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.appAmt 申请金额
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.appVol 申请份额
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.appDtm 申请日期时间
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.payStatus 付款状态
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.orderStatus 订单状态
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.taTradeDt TA交易日期
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.fee 手续费
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.mBusiCode 中台业务码
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.redeemDirection 赎回去向
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.scaleType 销售类型: 1-直销; 2-代销
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.divMode 分红方式
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.ackVol 确认份额
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.ackAmt 确认金额
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.ackDt 确认日期
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.memo 备注字段
     * @apiSuccess (响应结果) {Number} dealOrderList.subDealOrderBeans.nav 净值
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.advanceFlag 递延标识
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.productType 产品类型
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.submitTaDt 上报TA日期
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.productSubType 产品子类型
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.isVolTansfer 是否份额结转 1是，0否
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.mergeSubmitFlag 合并上报标识 1-合并上报
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.currency 币种
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.highFundInvPlanFlag 是否私募定投 0-不是 1-是
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} dealOrderList.subDealOrderBeans.sffqcl 是否分期成立（证券类有此标识）
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"fvl4lw78","hasHZProduct":"1GlnCyspta","totalPage":949,"pageNo":186,"description":"k0","hasHKProduct":"p","totalCount":8381,"dealOrderList":[{"appAmt":3670.*************,"highFundInvPlanFlag":"7zlAT","fee":8326.************,"mergeSubmitFlag":"IwElO6Wt","bankAcct":"3qnaj06","txAckFlag":"Dtm5iPaLQ","orderStatus":"78WKyDBMH","taTradeDt":"0","memo":"PE6jCY","disCode":"wM96PfS","continuanceFlag":"KVdN7wnk","mBusiCode":"HJd","advanceFlag":"Gij","productSubType":"XfcgPCfZHo","sffqcl":"E","productName":"vlWUjZn4lY","paymentType":"RnPTni2","productAttr":"H","redeemDirection":"LWpgv","ackAmt":7827.************,"appVol":1064.*************,"txAcctNo":"fLg6qYaxP7","appDtm":*************,"cpAcctNo":"46V","currency":"ej2ROUFP0n","hkSaleFlag":"Tevi2fC","isVolTansfer":"WM","productType":"CLwpej","bankCode":"hrE","ackVol":2793.*************,"nav":6996.************,"subProductCode":"I5x","submitTaDt":"ElAK3S77y","subDealOrderBeans":[{"appAmt":7269.************,"highFundInvPlanFlag":"NEn8","fee":6438.************,"mergeSubmitFlag":"zsnJH","bankAcct":"xeTgA85N","orderStatus":"Ul","taTradeDt":"JZ","memo":"jp","disCode":"5y4u","mBusiCode":"S","advanceFlag":"c0xVLYl7","productSubType":"n","sffqcl":"hpfBT","productName":"mHhBUi","paymentType":"9kSlo9k","productAttr":"VZ4N","redeemDirection":"M","ackAmt":9402.**********,"appVol":2029.*************,"txAcctNo":"RH6B7Ab","appDtm":*************,"cpAcctNo":"2pczQK57iW","currency":"zWRdoC","hkSaleFlag":"RmKTXJUFLT","isVolTansfer":"mNLm9x","productType":"mvn9D","bankCode":"TbszIe","ackVol":7565.************,"subProductCode":"vQmk5GS8s","nav":2988.************,"submitTaDt":"Hu","divMode":"gpQ","dealNo":"Wm","productCode":"k9d5P","scaleType":"7cMp","ackDt":"yxhq","payStatus":"RNUJTc"}],"divMode":"C","dealNo":"ABK","productCode":"x8Ee3","scaleType":"1MtJ","joinDt":"Z","isNoTradeTransfer":"mjq9","ackDt":"3Eje7Y","stageFlag":"nqVmRAQ","payStatus":"rZmzNDxr8b","transferPrice":975.************}]}
     */
    @Override
    public QueryDealOrderListResponse execute(QueryDealOrderListRequest request) {
        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }

        QueryDealOrderListResponse response = new QueryDealOrderListResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        // 账号处理
        String txAcctNo = request.getTxAcctNo();
        String hbOneNo = request.getHbOneNo();
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
        }
        logger.info("QueryDealOrderListFacadeService|execute() txAcctNo:{}, hbOneNo:{}", txAcctNo, hbOneNo);

        // 分页参数
        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();

        // 创建请求条件对象
        QueryDealOrderListCondition queryCondition = createQueryCondition(request, txAcctNo, hbOneNo);

        // 是否有香港产品
        int hkDealNum = dealOrderRepository.countHkDealNum(queryCondition);
        if (hkDealNum > 0) {
            response.setHasHKProduct(YesOrNoEnum.YES.getCode());
        } else {
            response.setHasHKProduct(YesOrNoEnum.NO.getCode());
        }

        // 是否有好臻产品
        int hzDealNum = dealOrderRepository.countHzDealNum(queryCondition);
        if (hzDealNum > 0) {
            response.setHasHZProduct(YesOrNoEnum.YES.getCode());
        } else {
            response.setHasHZProduct(YesOrNoEnum.NO.getCode());
        }
        // 查询交易记录
        Page<DealOrderVo> pageList = dealOrderRepository.queryDealOrderListWithDirect(queryCondition, pageNo, pageSize);
        if (pageList == null) {
            pageList = new Page<>();
        }
        // 处理合并上报单汇总信息
        queryMergeSubmitOrderService.processMergeSubmitOrder(pageList);

        //强制取消付款成功的递延订单特殊处理
        Long totalCount = processAdvanceOrder(request, pageList, queryCondition);
        response.setPageNo(pageNo);
        response.setTotalPage(pageList.getPages());
        if (CollectionUtils.isEmpty(pageList) || 0L == totalCount) {
            return response;
        }

        //处理递延订单状态
        processAdvanceOrderStatus(pageList.getResult());

        // 处理数据
        processData(request, pageList, response, txAcctNo);

        response.setTotalCount(totalCount);
        return response;
    }

    /**
     * processData:(处理数据)
     *
     * @param pageList
     * @param response
     * @param txAcctNo
     * <AUTHOR>
     * @date 2017年7月12日 下午5:19:59
     */
    private void processData(QueryDealOrderListRequest request, Page<DealOrderVo> pageList, QueryDealOrderListResponse response, String txAcctNo) {
        List<DealOrderVo> requstList = pageList.getResult();
        if (CollectionUtils.isEmpty(requstList)) {
            return;
        }

        Map<String, QueryCustBankCardResult> bankInfoMap = new HashMap<String, QueryCustBankCardResult>();
        Map<String, HighProductBaseInfoBean> productInfoMap = new HashMap<String, HighProductBaseInfoBean>();
        for (DealOrderVo dealOrderVo : requstList) {
            setInfo(bankInfoMap, productInfoMap, dealOrderVo);
        }


        if (!bankInfoMap.isEmpty()) {
            final CountDownLatch bankInfoLatch = new CountDownLatch(bankInfoMap.size());
            for (QueryCustBankCardResult queryCustBankCardResult : bankInfoMap.values()) {
                CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, bankInfoLatch, txAcctNo, request.getDisCodeList().get(0),
                        queryCustBankCardResult.getCpAcctNo(), queryCustBankCardResult, request.getOutletCode()));
            }

            try {
                bankInfoLatch.await();
            } catch (InterruptedException e) {
                logger.error("QueryDealOrderListFacadeService|latch.await exception.", e);
                Thread.currentThread().interrupt();
            }
        }

        if (!productInfoMap.isEmpty()) {
            final CountDownLatch productLatch = new CountDownLatch(productInfoMap.size());
            for (HighProductBaseInfoBean highProductBaseBean : productInfoMap.values()) {
                CommonThreadPool.submit(new QueryProductInfoTask(queryHighProductOuterService, highProductBaseBean, productLatch));
            }

            try {
                productLatch.await();
            } catch (InterruptedException e) {
                logger.error("QueryDealOrderListFacadeService|latch.await exception.", e);
                Thread.currentThread().interrupt();
            }

        }

        // 产品代码Set
        Set<String> queryProductCodeSet = new HashSet<String>();
        for (DealOrderVo dealOrderVo : requstList) {
            queryProductCodeSet.add(dealOrderVo.getProductCode());
        }

        // 批量查询产品基本信息
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(new ArrayList<>(queryProductCodeSet));

        List<DealOrderBean> dealOrderList = new ArrayList<>();
        final CountDownLatch latch = new CountDownLatch(requstList.size());
        for (DealOrderVo dealOrderVo : requstList) {
            DealOrderBean dealOrderBean = new DealOrderBean();
            BeanUtils.copyProperties(dealOrderVo, dealOrderBean);
            // 多线程处理订单信息数据
            CommonThreadPool.submit(new DealOrderProcessTask(dealOrderVo.getScaleType(),
                    dealOrderBean, latch, productInfoMap, bankInfoMap, highProductDbInfoBeanMap,
                    querySimuProductInfoOuterService, highRedeemSplitDtlRepository, highDealOrderDtlRepository));
            dealOrderList.add(dealOrderBean);
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryDealOrderListFacadeService|latch.await exception.", e);
            Thread.currentThread().interrupt();
        }
        for (DealOrderBean dealOrderBean : dealOrderList) {
            String memo = buildMemo(bankInfoMap, dealOrderBean.getMemo());
            dealOrderBean.setMemo(memo);
            String businessCodeDesc = BusinessCodeEnum.getName(dealOrderBean.getmBusiCode());
            if (StringUtils.isNotBlank(businessCodeDesc)) {
                dealOrderBean.setBusinessDesc(businessCodeDesc);
            } else {
                dealOrderBean.setBusinessDesc(dealOrderBean.getmBusiCode());
            }
        }

        response.setDealOrderList(dealOrderList);
    }

    private void setInfo(Map<String, QueryCustBankCardResult> bankInfoMap, Map<String, HighProductBaseInfoBean> productInfoMap, DealOrderVo dealOrderVo) {
        // 代销的Ta交易
        if (ScaleTypeEnum.CONSIGNMENT.getCode().equals(dealOrderVo.getScaleType()) && MDataDic.TA_TRADE_BUSI_CODE_SET.contains(dealOrderVo.getmBusiCode())) {
            if (!productInfoMap.containsKey(dealOrderVo.getProductCode())) {
                HighProductBaseInfoBean highProductBaseBean = new HighProductBaseInfoBean();
                highProductBaseBean.setFundCode(dealOrderVo.getProductCode());
                productInfoMap.put(dealOrderVo.getProductCode(), highProductBaseBean);
            }
            if (!bankInfoMap.containsKey(dealOrderVo.getCpAcctNo())) {
                QueryCustBankCardResult queryCustBankCardResult = new QueryCustBankCardResult();
                queryCustBankCardResult.setCpAcctNo(dealOrderVo.getCpAcctNo());
                bankInfoMap.put(dealOrderVo.getCpAcctNo(), queryCustBankCardResult);
            }
        }
        //迁移的多卡交易处理
        if (!StringUtils.isEmpty(dealOrderVo.getMemo())) {
            processMutiCard(bankInfoMap, dealOrderVo.getMemo());
        }
    }


    /**
     * createQueryCondition:(创建请求条件对象)
     *
     * @param request
     * @param txAcctNo
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 下午5:18:26
     */
    private QueryDealOrderListCondition createQueryCondition(QueryDealOrderListRequest request, String txAcctNo, String hbOneNo) {
        // 组装查询参数
        QueryDealOrderListCondition queryCondition = new QueryDealOrderListCondition();
        queryCondition.setTxAcctNo(txAcctNo);
        queryCondition.setHbOneNo(hbOneNo);
        queryCondition.setNotFilterHkFund(request.getNotFilterHkFund());
        queryCondition.setNotFilterHzFund(request.getNotFilterHzFund());
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            queryCondition.setDisCodeList(request.getDisCodeList());
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            queryCondition.setDisCodeList(disCodeList);
        }
        queryCondition.setCpAcctNo(request.getCpAcctNo());
        queryCondition.setProductCode(request.getProductCode());
        queryCondition.setProductName(request.getProductName());
        queryCondition.setFundNameOrCode(request.getFundNameOrCode());

        // 业务码
        String[] mBusiCodeArr = request.getmBusiCodeArr();
        if (mBusiCodeArr != null && mBusiCodeArr.length > 0) {
            if (!StringUtils.isEmpty(hbOneNo)) {
                List<String> busiCodeList = new ArrayList<String>();
                for (String mBusiCode : mBusiCodeArr) {
                    if (StringUtils.isNotBlank(mBusiCode)) {
                        busiCodeList.add(mBusiCode.substring(1));
                    }
                }
                queryCondition.setBusiCodeList(busiCodeList);
            }

            if (!StringUtils.isEmpty(txAcctNo)) {
                queryCondition.setmBusiCodeList(Arrays.asList(mBusiCodeArr));
            }
        }

        // 订单状态
        if (!StringUtils.isEmpty(txAcctNo)) {
            String[] orderStatusArr = request.getOrderStatusArr();
            if (orderStatusArr != null && orderStatusArr.length > 0) {
                queryCondition.setOrderStatusList(Arrays.asList(orderStatusArr));
            }

        }

        queryCondition.setAppBeginDtm(request.getAppBeginDtm());
        if (request.getAppEndDtm() != null) {
            queryCondition.setAppEndDtm(DateUtils.addDay(request.getAppEndDtm(), 1));
        }

        queryCondition.setDealNo(request.getDealNo());

        return queryCondition;
    }

    /**
     * TODO
     * processAdvanceOrder:(递延订单特殊处理)
     *
     * @param pageList
     * @param queryCondition
     * @return
     * <AUTHOR>
     * @date 2017年11月30日 上午9:46:08
     */
    private Long processAdvanceOrder(QueryDealOrderListRequest request, Page<DealOrderVo> pageList, QueryDealOrderListCondition queryCondition) {
        logger.info("QueryDealOrderListFacadeService|processAdvanceOrder|start");
        Long totalCount = (pageList == null ? 0L : pageList.getTotal());
        if (queryCondition == null || CollectionUtils.isEmpty((queryCondition.getOrderStatusList()))) {
            return totalCount;
        }
        if (queryCondition.getOrderStatusList().contains(OrderStatusEnum.APP_SUCCESS.getCode())
                && !queryCondition.getOrderStatusList().contains(OrderStatusEnum.FORCE_CANCELED.getCode())) {

            Long succCount = processAdvanceSucc(request, pageList, queryCondition);
            logger.info("QueryDealOrderListFacadeService|processAdvanceOrder|succCount:{}", succCount);
            totalCount = totalCount + succCount;

        } else if (!queryCondition.getOrderStatusList().contains(OrderStatusEnum.APP_SUCCESS.getCode())
                && queryCondition.getOrderStatusList().contains(OrderStatusEnum.FORCE_CANCELED.getCode())) {
            Long cancelCount = processAdvanceForceCancel(request, pageList, queryCondition);
            logger.info("QueryDealOrderListFacadeService|processAdvanceOrder|cancelCount:{}", cancelCount);
            totalCount = totalCount - cancelCount;
        }
        logger.info("QueryDealOrderListFacadeService|processAdvanceOrder|totalCount:{}", totalCount);
        return totalCount;
    }

    /**
     * TODO
     * processAdvanceSucc:(处理强制取消付款成功的递延单为成功订单)
     *
     * @param request
     * @param pageList
     * @param queryCondition
     * <AUTHOR>
     * @date 2017年11月30日 上午10:20:41
     */
    private Long processAdvanceSucc(QueryDealOrderListRequest request, Page<DealOrderVo> pageList, QueryDealOrderListCondition queryCondition) {
        logger.info("QueryDealOrderListFacadeService|processAdvanceSucc|start");
        List<DealOrderVo> advanceOrderList = dealOrderRepository.queryAdvanceDealOrderList(queryCondition);
        logger.info("QueryDealOrderListFacadeService|processAdvanceSucc|advanceOrderList", JSON.toJSON(advanceOrderList));
        if (!CollectionUtils.isEmpty(advanceOrderList)) {
            if (CollectionUtils.isEmpty(pageList)) {
                pageList.addAll(advanceOrderList);
                pageList.setPageNum(1);
                pageList.setPageSize(advanceOrderList.size());
                pageList.setTotal(advanceOrderList.size());

            } else {
                if (pageList.getPages() == pageList.getPageNum()) {
                    pageList.addAll(advanceOrderList);
                }
            }
            return (long) advanceOrderList.size();
        } else {
            return 0L;
        }
    }

    /**
     * TODO
     * 从强制取消的递延订单中移除强制取消付款失败的递延订单
     * processAdvanceForceCancel:(处理强制取消的递延订单)
     *
     * @param request
     * @param pageList
     * @param queryCondition
     * <AUTHOR>
     * @date 2017年11月30日 上午10:23:17
     */
    private Long processAdvanceForceCancel(QueryDealOrderListRequest request, Page<DealOrderVo> pageList, QueryDealOrderListCondition queryCondition) {
        logger.info("QueryDealOrderListFacadeService|processAdvanceForceCancel|start");
        List<DealOrderVo> advanceOrderList = dealOrderRepository.queryAdvanceDealOrderList(queryCondition);
        logger.info("QueryDealOrderListFacadeService|processAdvanceForceCancel|advanceOrderList:{}", JSON.toJSONString(advanceOrderList));
        if (!CollectionUtils.isEmpty(advanceOrderList)) {
            Set<String> advanceOrderNoSet = new HashSet<String>();
            for (DealOrderVo advanceOrder : advanceOrderList) {
                advanceOrderNoSet.add(advanceOrder.getDealNo());
            }
            if (pageList != null && !CollectionUtils.isEmpty(pageList.getResult())) {
                pageList.getResult().removeIf(dealOrderVo -> advanceOrderNoSet.contains(dealOrderVo.getDealNo()));
            }
            return (long) advanceOrderList.size();
        } else {
            return 0L;
        }
    }

    /**
     * processAdvanceOrderStatus:处理递延订单状态)
     *
     * @param advanceDealOrderList
     * <AUTHOR>
     * @date 2017年11月30日 上午11:01:44
     */
    private void processAdvanceOrderStatus(List<DealOrderVo> advanceDealOrderList) {
        if (CollectionUtils.isEmpty(advanceDealOrderList)) {
            return;
        }
        for (DealOrderVo advanceDealOrder : advanceDealOrderList) {
            if (AdvanceFlagEnum.TRADE_ADVENCE.getCode().equals(advanceDealOrder.getAdvanceFlag())) {
                if (OrderStatusEnum.FORCE_CANCELED.getCode().equals(advanceDealOrder.getOrderStatus())
                        && PayStatusEnum.PAY_FAIL.getCode().equals(advanceDealOrder.getPayStatus())) {

                    advanceDealOrder.setOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode());
                    advanceDealOrder.setPayStatus(PayStatusEnum.PAYING.getCode());
                }
            }
        }

    }

}


