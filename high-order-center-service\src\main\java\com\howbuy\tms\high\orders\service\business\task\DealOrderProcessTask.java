/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.DivModeEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.outerservice.simu.productinfo.QuerySimuProductInfoOuterService;
import com.howbuy.tms.high.orders.dao.po.HighRedeemSplitDtlPo;
import com.howbuy.tms.high.orders.dao.vo.QueryHighDealOrderDtlVo;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse.DealOrderBean;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.bean.SubDealOrderBean;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.HighRedeemSplitDtlRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;


/**
 * 
 * @description:(订单信息处理任务)
 * @reason:
 * <AUTHOR>
 * @date 2017年7月12日 下午5:44:11
 * @since JDK 1.7
 */
public class DealOrderProcessTask implements Callable<RuntimeException>{
    
    private static final Logger logger = LogManager.getLogger(DealOrderProcessTask.class);
    
    Map<String,QueryCustBankCardResult> bankInfoMap;
    Map<String,HighProductBaseInfoBean> productInfoMap;
    Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap;
    
    private String scaleType;
    private DealOrderBean dealOrderBean;
    private CountDownLatch latch;
    private QuerySimuProductInfoOuterService querySimuProductInfoOuterService;
    private HighRedeemSplitDtlRepository highRedeemSplitDtlRepository;
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    
    public DealOrderProcessTask(String scaleType,
                                DealOrderBean dealOrderBean, CountDownLatch latch,
                                Map<String,HighProductBaseInfoBean> productInfoMap, Map<String,QueryCustBankCardResult> bankInfoMap,
                                Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap,
                                QuerySimuProductInfoOuterService querySimuProductInfoOuterService,
                                HighRedeemSplitDtlRepository highRedeemSplitDtlRepository, HighDealOrderDtlRepository highDealOrderDtlRepository){
        this.scaleType = scaleType;
        this.dealOrderBean = dealOrderBean;
        this.latch = latch;
        this.productInfoMap = productInfoMap;
        this.bankInfoMap = bankInfoMap;
        this.highProductDbInfoBeanMap = highProductDbInfoBeanMap;
        this.querySimuProductInfoOuterService = querySimuProductInfoOuterService;
        this.highRedeemSplitDtlRepository = highRedeemSplitDtlRepository;
        this.highDealOrderDtlRepository = highDealOrderDtlRepository;
    }
    @Override
    public RuntimeException call() throws Exception {
        try{
            //修改分红方式订单不展示银行卡
            if(BusinessCodeEnum.DIV_MODE_CHANGE.getMCode().equals(dealOrderBean.getmBusiCode())){
                dealOrderBean.setBankAcct(null);
                dealOrderBean.setBankCode(null);
            }
            // 代销的Ta交易
            HighProductBaseInfoBean highProductBaseBean = productInfoMap.get(dealOrderBean.getProductCode());
            if(highProductBaseBean != null) {
                if (ScaleTypeEnum.CONSIGNMENT.getCode().equals(scaleType) && MDataDic.TA_TRADE_BUSI_CODE_SET.contains(dealOrderBean.getmBusiCode())) {
                        dealOrderBean.setProductName(highProductBaseBean.getFundAttr());
                        dealOrderBean.setProductType(highProductBaseBean.getFundType());
                        dealOrderBean.setProductSubType(highProductBaseBean.getFundSubType());
                }
            }

            // 设置香港标识
            HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoBeanMap.get(dealOrderBean.getProductCode());
            if(highProductDbInfoBean != null){
                String hkSaleFlag = highProductDbInfoBean.getHkSaleFlag() == null ? YesOrNoEnum.NO.getCode() : highProductDbInfoBean.getHkSaleFlag();
                dealOrderBean.setHkSaleFlag(hkSaleFlag);
                dealOrderBean.setProductAttr(highProductDbInfoBean.getFundAttr());
            }else {
                dealOrderBean.setHkSaleFlag(YesOrNoEnum.NO.getCode());
            }

            // 设置代销子基金代码
            if(highProductDbInfoBean != null){
                if(YesOrNoEnum.YES.getCode().equals(highProductDbInfoBean.getStageEstablishFlag()) &&
                        StringUtils.isEmpty(dealOrderBean.getSubProductCode())){
                    // 获取子基金代码
                    String zjjdm = querySimuProductInfoOuterService.getSubJjdmsByMjjdm(dealOrderBean.getProductCode(),dealOrderBean.getJoinDt());
                    dealOrderBean.setSubProductCode(zjjdm);
                }
            }

            // 代销的Ta交易 及 脱敏的卡号
            QueryCustBankCardResult queryCustBankCardResult = bankInfoMap.get(dealOrderBean.getCpAcctNo());
            if(queryCustBankCardResult != null){
                dealOrderBean.setBankAcct(queryCustBankCardResult.getBankAcct());
                dealOrderBean.setBankCode(queryCustBankCardResult.getBankCode());
            }
            
            if (!StringUtils.isEmpty(dealOrderBean.getmBusiCode())) {
                // TA 分红交易, 设置分红状态
                if (BusinessCodeEnum.DIV.getMCode().equals(dealOrderBean.getmBusiCode())) {
                    if (dealOrderBean.getAppVol() != null && dealOrderBean.getAppVol().compareTo(BigDecimal.ZERO) > 0) {
                        dealOrderBean.setDivMode(DivModeEnum.DIV_MODE_VOL.getCode());
                    } else {
                        dealOrderBean.setDivMode(DivModeEnum.DIV_MODE_AMT.getCode());
                    }
                }
            }

            // tradeStatus 巨额赎回确认中
            if(YesOrNoEnum.YES.getCode().equals(dealOrderBean.getContinuanceFlag()) &&
                    "2".equals(dealOrderBean.getTxAckFlag())){
                dealOrderBean.setOrderStatus("jesxing");
            }

            // 查询赎回拆单明细
            checkMergeRedeem();

        }catch(RuntimeException ex){
            logger.error("DealOrderProcessTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

    /**
     * 校验合并赎回
     */
    private void checkMergeRedeem() {
        if(BusinessCodeEnum.REDEEM.getMCode().equals(dealOrderBean.getmBusiCode()) &&
                (YesOrNoEnum.YES.getCode().equals(dealOrderBean.getContinuanceFlag()) || YesOrNoEnum.YES.getCode().equals(dealOrderBean.getStageFlag()))){
            List<String> dealNos = new ArrayList<>();
            if(YesOrNoEnum.YES.getCode().equals(dealOrderBean.getMergeSubmitFlag())){
                // 合并赎回
                List<QueryHighDealOrderDtlVo> orderList = highDealOrderDtlRepository.selectMergeSubmitOrderDtlByMainDealNo(dealOrderBean.getDealNo());
                dealNos = orderList.stream().map(QueryHighDealOrderDtlVo::getDealNo).collect(Collectors.toList());
            }else {
                // 非合并赎回
                dealNos.add(dealOrderBean.getDealNo());
            }
            List<HighRedeemSplitDtlPo> splitDtlList = highRedeemSplitDtlRepository.selectByDealNo(dealNos);
            List<SubDealOrderBean> subDealOrderBeans = new ArrayList<>();
            if(!CollectionUtils.isEmpty(splitDtlList)){
                SubDealOrderBean subDealOrderBean = null;
                for(HighRedeemSplitDtlPo dtlPo : splitDtlList){
                    subDealOrderBean = new SubDealOrderBean();
                    BeanUtils.copyProperties(dealOrderBean,subDealOrderBean);
                    BeanUtils.copyProperties(dtlPo,subDealOrderBean);
                    subDealOrderBean.setSubProductCode(dtlPo.getSubFundCode());
                    if("4".equals(dtlPo.getTxAckFlag())){
                        // 确认成功
                        subDealOrderBean.setOrderStatus("3");
                    }else {
                        // 确认失败
                        subDealOrderBean.setOrderStatus("4");
                    }
                    subDealOrderBeans.add(subDealOrderBean);
                }
                dealOrderBean.setSubDealOrderBeans(subDealOrderBeans);
            }
        }
    }

}

