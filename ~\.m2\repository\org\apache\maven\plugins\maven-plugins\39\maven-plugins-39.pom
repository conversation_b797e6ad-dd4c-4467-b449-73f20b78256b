<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>39</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven.plugins</groupId>
  <artifactId>maven-plugins</artifactId>
  <packaging>pom</packaging>

  <name>Apache Maven Plugins</name>
  <description>Maven Plugins</description>
  <url>https://maven.apache.org/plugins/</url>

  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-builds.apache.org/job/Maven/job/maven-box/job/maven-plugins/</url>
  </ciManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/plugins-archives/</url>
    </site>
  </distributionManagement>

  <properties>
    <maven.site.path>plugins-archives/${project.artifactId}-LATEST</maven.site.path>
  </properties>

  <dependencies>
    <!-- dependencies to annotations -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <releaseProfiles>apache-release,run-its</releaseProfiles>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <executions>
            <execution>
              <id>default-descriptor</id>
              <phase>process-classes</phase>
              <configuration>
                <internalJavadocBaseUrl>./apidocs/</internalJavadocBaseUrl>
              </configuration>
            </execution>
            <execution>
              <id>generate-helpmojo</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <!-- publish mono-module site with "mvn site-deploy" -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <configuration>
            <!-- don't deploy site with maven-site-plugin -->
            <skipDeploy>true</skipDeploy>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <!-- no need for site:stage, use target/site -->
          <content>${project.reporting.outputDirectory}</content>
        </configuration>
        <executions>
          <execution>
            <!-- deploy site with maven-scm-publish-plugin -->
            <id>scm-publish</id>
            <goals>
              <goal>publish-scm</goal>
            </goals>
            <phase>site-deploy</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>ensure-no-container-api</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.codehaus.plexus:plexus-component-api</exclude>
                  </excludes>
                  <message>The new containers are not supported. You probably added a dependency that is missing the exclusions.</message>
                </bannedDependencies>
              </rules>
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>quality-checks</id>
      <activation>
        <property>
          <name>quality-checks</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-docck-plugin</artifactId>
            <executions>
              <execution>
                <id>docck-check</id>
                <goals>
                  <goal>check</goal>
                </goals>
                <phase>verify</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>run-its</id>
      <properties>
        <!-- when testing with JDK9, change these values to 1.6 from cmdline -->
        <invoker.maven.compiler.source>${maven.compiler.source}</invoker.maven.compiler.source>
        <invoker.maven.compiler.target>${maven.compiler.target}</invoker.maven.compiler.target>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <configuration>
              <debug>true</debug>
              <projectsDirectory>src/it</projectsDirectory>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <preBuildHookScript>setup</preBuildHookScript>
              <postBuildHookScript>verify</postBuildHookScript>
              <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              <settingsFile>src/it/settings.xml</settingsFile>
              <pomIncludes>
                <pomInclude>*/pom.xml</pomInclude>
              </pomIncludes>
              <properties>
                <maven.compiler.source>${invoker.maven.compiler.source}</maven.compiler.source>
                <maven.compiler.target>${invoker.maven.compiler.target}</maven.compiler.target>
              </properties>
            </configuration>
            <executions>
              <execution>
                <id>integration-test</id>
                <goals>
                  <goal>install</goal>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-plugin-report-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
