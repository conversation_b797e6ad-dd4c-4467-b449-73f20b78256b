<?xml version='1.0' encoding='UTF-8'?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>maven-plugins</artifactId>
    <groupId>org.apache.maven.plugins</groupId>
    <version>31</version>
    <relativePath>../../pom/maven/maven-plugins/pom.xml</relativePath>
  </parent>

  <artifactId>maven-resources-plugin</artifactId>
  <version>3.1.0</version>
  <packaging>maven-plugin</packaging>

  <name>Apache Maven Resources Plugin</name>
  <description>
    The Resources Plugin handles the copying of project resources to the output
    directory. There are two different kinds of resources: main resources and test resources. The
    difference is that the main resources are the resources associated to the main
    source code while the test resources are associated to the test source code.
    Thus, this allows the separation of resources for the main source code and its
    unit tests.
  </description>
  <inceptionYear>2001</inceptionYear>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-resources-plugin.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-resources-plugin.git</developerConnection>
    <url>https://github.com/apache/maven-resources-plugin/tree/${project.scm.tag}</url>
    <tag>maven-resources-plugin-3.1.0</tag>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/MRESOURCES</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/maven-box/job/maven-resources-plugin/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <mavenFilteringVersion>3.1.1</mavenFilteringVersion>
    <mavenVersion>3.0</mavenVersion>
    <surefire.version>2.21.0</surefire.version>
    <javaVersion>7</javaVersion>
  </properties>

  <contributors>
    <contributor>
      <name>Graham Leggett</name>
    </contributor>
  </contributors>
    
  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>${mavenVersion}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.1.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-filtering</artifactId>
      <version>${mavenFilteringVersion}</version>
    </dependency>
    <!-- Upgrade of transitive commons-io 2.4 of maven-shared-utils of maven-filtering. -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.5</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-interpolation</artifactId>
      <version>1.24</version>
    </dependency>

    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-compat</artifactId>
      <version>${mavenVersion}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>2.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <configuration>
                <debug>true</debug>
                <postBuildHookScript>verify</postBuildHookScript>
                <preBuildHookScript>setup</preBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <goals>
                  <goal>clean</goal>
                  <goal>process-test-resources</goal>
                </goals>
                <settingsFile>src/it/settings.xml</settingsFile>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <properties>
                  <execProps>fromExecProps</execProps>
                </properties>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <!--
           ! The following is used within the src/it/user-filters test.
          -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-component-metadata</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>generate-test-metadata</goal>
                </goals>
              </execution>
            </executions>
          </plugin>          
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
